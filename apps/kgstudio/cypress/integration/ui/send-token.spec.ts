/// <reference types="cypress" />
function selectBlockchainTokenAndAmount(blockchain: string, token: string, amount: string) {
  cy.getBySel('send-input-blockchain').click();
  cy.get('[role=option]').contains(blockchain).click();
  cy.getBySel('send-input-token').click();
  cy.get('[role=option]').contains(token).click();
  cy.getBySel('send-input-amount').type(amount);
}

function submitAndConfirmSend() {
  cy.getBySel('send-submit-button').click();
  cy.getBySel('send-confirm-dialog').should('exist');
  cy.getBySel('send-confirm-dialog-confirm').click();
}

describe('[UI] Send Token Page', () => {
  beforeEach(() => {
    cy.interceptedGetTokens();
    cy.setLoggedIn({
      asset_pro: {
        remain_limit: 12345,
        daily_transfer_limit: 100000,
        transfer_approval_threshold: 10000,
      },
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/accounts`, {
        fixture: '/treasury/accounts.json',
      }).as('getKGAccounts');
    });
    cy.interceptedGetTokens();
    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/asset_prices*`, {
      fixture: '/treasury/price.json',
    }).as('getPrice');
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/proxy_3rd_party`, {
      fixture: 'transfer/kya.json',
    }).as('interceptedKya');
    cy.visit('/en-US/asset/transfer');
    cy.wait('@getKGAccounts');
    cy.wait('@getPrice');
    // FIXME: should intercept useTokenBalance, useNativeBalanceAndFee
    cy.intercept('POST', `https://eth-sepolia.g.alchemy.com/v2/*`, {
      fixture: 'transfer/token-balance.json',
    }).as('interceptedTokenBalance');
    // cy.intercept('POST', `https://cloudflare-eth.com/`, {
    //   fixture: 'transfer/gas-balance.json',
    // }).as('interceptedGasBalance');
  });

  it.skip(`Given:
        - Logged in as organization member
      When:
        - Enter invalid email/phone/address
      Then:
        - Should display error message
      `, () => {
    cy.getBySel('send-input-email').type('abc123');
    cy.getBySel('send-input-email').blur();
    cy.contains('Please enter a valid email').should('be.visible');

    cy.getBySel('send-type-radio').get('button[value="phone"]').click();
    cy.getBySel('send-input-phone').type('999');
    cy.getBySel('send-input-phone').blur();
    cy.contains('Please enter a valid phone').should('be.visible');

    cy.getBySel('send-type-radio').get('button[value="address"]').click();
    cy.getBySel('send-input-address').type('0x');
    cy.getBySel('send-input-address').blur();
    cy.contains('Please enter a valid wallet address').should('be.visible');
  });

  it.skip(`Given:
        - Logged in as organization member
      When:
        - Enter an email that does not exist in the KG system(User not found, which means the user without KG UID)
        - Enter an phone that does not exist in the organization's customer list(Customer not found)
      Then:
        - Should display error message
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept(
      'GET',
      `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/customer?email=not_exist_user%40mail.com`,
      { statusCode: 404, fixture: '/customer/customer-exist-false-2100.json' },
    ).as('interceptedEmailNotExist');

    cy.intercept(
      'GET',
      `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/customer?phone_number=%2B886900000000`,
      {
        statusCode: 404,
        fixture: '/customer/customer-exist-false-7016.json',
      },
    ).as('interceptedPhoneNotExist');

    selectBlockchainTokenAndAmount('Sepolia Testnet', 'USDC', '0.1');

    cy.getBySel('send-input-email').type('<EMAIL>');
    cy.getBySel('send-input-email').blur();
    cy.getBySel('risk-scan-button').should('be.enabled');
    cy.getBySel('risk-scan-button').click();
    cy.wait('@interceptedEmailNotExist');
    cy.contains("Sorry, we can't find this user.").should('be.visible');

    cy.getBySel('send-type-radio').get('button[value="phone"]').click();
    cy.getBySel('send-input-phone').type('900000000');
    cy.getBySel('send-input-phone').blur();
    cy.getBySel('risk-scan-button').should('be.enabled');
    cy.getBySel('risk-scan-button').click();
    cy.wait('@interceptedPhoneNotExist');
    cy.contains(
      'Sorry, this user has not yet registered for your service. If you would like to transfer funds to this user, please ask them to register your KYC form or Wallet first.',
    ).should('be.visible');
  });

  it.skip(`Given:
        - Logged in as organization member
      When:
        - Enter a valid email/phone/address
      Then:
        - Should display verified KYC status(except send by address) and enabled Send button
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept(
      'GET',
      `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/customer?email=verified%40mail.com`,
      {
        statusCode: 200,
        fixture: '/customer/customer-exist-kyc-verified.json',
      },
    ).as('interceptedEmailKycVerified');
    cy.intercept(
      'GET',
      `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/customer?phone_number=%2B886900000000`,
      {
        statusCode: 200,
        fixture: '/customer/customer-exist-kyc-verified.json',
      },
    ).as('interceptedPhoneKycVerified');

    // chain, token, amount
    selectBlockchainTokenAndAmount('Sepolia Testnet', 'USDC', '0.1');

    cy.wait('@interceptedTokenBalance');

    // email
    cy.getBySel('send-input-email').type('<EMAIL>');
    cy.getBySel('send-input-email').blur();
    cy.getBySel('risk-scan-button').should('be.enabled');
    cy.getBySel('risk-scan-button').click();
    cy.wait('@interceptedEmailKycVerified');
    cy.contains('Verified').should('be.visible');
    cy.contains('This user has passed KYC verification!').should('be.visible');
    cy.get(`a[href="https://sepolia.etherscan.io/address/0xabaaaaa"]`).should('exist');

    // phone
    cy.getBySel('send-input-email').clear();
    cy.getBySel('send-type-radio').get('button[value="phone"]').click();
    cy.getBySel('send-input-phone').type('900000000');
    cy.getBySel('send-input-phone').blur();
    cy.getBySel('risk-scan-button').should('be.enabled');
    cy.getBySel('risk-scan-button').click();
    cy.wait('@interceptedPhoneKycVerified');
    cy.contains('Verified').should('be.visible');
    cy.contains('This user has passed KYC verification!').should('be.visible');
    cy.get(`a[href="https://sepolia.etherscan.io/address/0xabaaaaa"]`).should('exist');

    // address
    cy.getBySel('send-input-phone').clear();
    cy.getBySel('send-type-radio').get('button[value="address"]').click();
    cy.getBySel('send-input-address').type('******************************************');
    cy.getBySel('send-input-address').blur();
    cy.getBySel('risk-scan-button').should('be.enabled');
    cy.getBySel('risk-scan-button').click();
    cy.getBySel('send-submit-button').should('be.enabled');
  });

  it.skip(`Given:
        - Logged in as organization member
      When:
        - Enter a valid email or valid phone
      Then:
        - Should display pending/unverified/rejected KYC status
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/customer?email=pending%40mail.com`, {
      statusCode: 200,
      fixture: '/customer/customer-exist-kyc-pending.json',
    }).as('interceptedEmailKycPending');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/customer?email=unverified*`, {
      statusCode: 200,
      fixture: '/customer/customer-exist-kyc-unverified.json',
    }).as('interceptedEmailKycUnverified');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/customer?email=rejected*`, {
      statusCode: 200,
      fixture: '/customer/customer-exist-kyc-rejected.json',
    }).as('interceptedEmailKycRejected');

    selectBlockchainTokenAndAmount('Sepolia Testnet', 'USDC', '0.1');

    cy.getBySel('send-input-email').type('<EMAIL>');
    cy.getBySel('send-input-email').blur();
    cy.getBySel('risk-scan-button').should('be.enabled');
    cy.getBySel('risk-scan-button').click();
    cy.wait('@interceptedEmailKycPending');
    cy.contains('Pending').should('be.visible');
    cy.contains(
      'Please note that the KYC review for this user is still being processed. Please complete the review as soon as possible and trade with caution.',
    ).should('be.visible');

    cy.getBySel('send-input-email').clear();
    cy.getBySel('send-input-email').type('<EMAIL>');
    cy.getBySel('send-input-email').blur();
    cy.getBySel('risk-scan-button').should('be.enabled');
    cy.getBySel('risk-scan-button').click();
    cy.getBySel('risk-scan-button').click();
    cy.wait('@interceptedEmailKycUnverified');
    cy.contains('Unverified').should('be.visible');
    cy.contains('This user has not yet passed KYC verification, please trade with caution.').should('be.visible');

    cy.getBySel('send-input-email').clear();
    cy.getBySel('send-input-email').type('<EMAIL>');
    cy.getBySel('send-input-email').blur();
    cy.getBySel('risk-scan-button').should('be.enabled');
    cy.getBySel('risk-scan-button').click();
    cy.getBySel('risk-scan-button').click();
    cy.wait('@interceptedEmailKycRejected');
    cy.contains('Rejected').should('be.visible');
    cy.contains('Please note that this user has failed the KYC review, please trade with caution.').should(
      'be.visible',
    );
  });

  it.skip(`Given:
        - Logged in as organization member
      When:
        - Enter a valid email
      Then:
        - Should send token successfully
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept(
      'GET',
      `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/customer?email=verified%40mail.com`,
      {
        statusCode: 200,
        fixture: '/customer/customer-exist-kyc-verified.json',
      },
    ).as('interceptedEmailKycVerified');

    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/transfer`, {
      delay: 100,
      statusCode: 200,
      fixture: '/transfer/transfer.json',
    }).as('interceptedTransfer');

    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/signed_urls`, {
      statusCode: 200,
      fixture: '/files/signed-urls.json',
    }).as('interceptedSignedUrls');

    cy.interceptedTxStatusLoadingThenSuccess();

    // chain, token, amount
    selectBlockchainTokenAndAmount('Sepolia Testnet', 'USDC', '0.01');

    cy.wait('@interceptedTokenBalance');

    // email
    cy.getBySel('send-input-email').type('<EMAIL>');
    cy.getBySel('send-input-email').blur();
    cy.getBySel('risk-scan-button').should('be.enabled');
    cy.getBySel('risk-scan-button').click();
    cy.wait('@interceptedEmailKycVerified');
    cy.contains('Verified').should('be.visible');

    cy.getBySel('transaction-note').find('textarea').type('test note');
    cy.getBySel('transaction-attachment')
      .find('input[type=file]')
      .selectFile('cypress/fixtures/files/image.png', { force: true });
    cy.getBySel('send-submit-button').should('be.enabled');

    // send token
    cy.getBySel('send-submit-button').click();
    cy.getBySel('send-confirm-dialog').should('exist');
    cy.getBySel('transaction-dialog-recipient').should('contain.text', 'Bella');
    cy.getBySel('transaction-dialog-note').should('contain.text', 'test note');
    cy.getBySel('transaction-dialog-attachment').find('img').should('exist');
    cy.getBySel('send-confirm-dialog-confirm').click();
    cy.wait('@interceptedSignedUrls');
    cy.wait('@interceptedTransfer')
      .its('request.body')
      .should('deep.equal', {
        chain_id: 'sepolia',
        contract_address: '******************************************',
        amount: '0.01',
        wallet_address: '0xabaaaaa',
        email: '<EMAIL>',
        display_name: 'Bella',
        kyc_status: 'verified',
        note: 'test note',
        attachments: ['transfer'],
      });
    cy.getBySel('transfer-submitted-text')
      .find('p')
      .should(
        'contain.text',
        'Your transaction request has been submitted successfully and will soon be able to view the transaction results.',
      );
  });

  it.skip(`Given:
        - Logged in as organization member
      When:
        - Enter a valid email
      Then:
        - Should fail to send token
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept(
      'GET',
      `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/customer?email=verified%40mail.com`,
      {
        statusCode: 200,
        fixture: '/customer/customer-exist-kyc-verified.json',
      },
    ).as('interceptedEmailKycVerified');

    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/transfer`, {
      delay: 100,
      statusCode: 200,
      fixture: '/transfer/transfer.json',
    }).as('interceptedTransfer');

    cy.interceptedTxStatusLoadingThenFailed();

    // chain, token, amount
    selectBlockchainTokenAndAmount('Sepolia Testnet', 'USDC', '0.1');

    // email
    cy.getBySel('send-input-email').type('<EMAIL>');
    cy.getBySel('send-input-email').blur();
    cy.getBySel('risk-scan-button').should('be.enabled');
    cy.getBySel('risk-scan-button').click();
    cy.wait('@interceptedEmailKycVerified');
    cy.contains('Verified').should('be.visible');
    cy.getBySel('send-submit-button').should('be.enabled');

    // send token
    submitAndConfirmSend();
    cy.wait('@interceptedTransfer').its('request.body').should('deep.equal', {
      chain_id: 'sepolia',
      contract_address: '******************************************',
      amount: '0.1',
      wallet_address: '0xabaaaaa',
      email: '<EMAIL>',
      display_name: 'Bella',
      kyc_status: 'verified',
    });
  });

  it(`Given:
        - Logged in as organization member
      When:
        - Enter a valid phone
      Then:
        - Should send token successfully
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept(
      'GET',
      `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/customer?phone_number=%2B886900000000`,
      {
        statusCode: 200,
        fixture: '/customer/customer-exist-kyc-verified.json',
      },
    ).as('interceptedPhoneKycVerified');

    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/transfer`, {
      delay: 100,
      statusCode: 200,
      fixture: '/transfer/transfer.json',
    }).as('interceptedTransfer');

    cy.interceptedTxStatusLoadingThenSuccess();

    // chain, token, amount
    selectBlockchainTokenAndAmount('Sepolia Testnet', 'USDC', '0.1');

    // phone
    cy.getBySel('send-type-radio').get('button[value="phone"]').click();
    cy.getBySel('send-input-phone').type('900000000');
    cy.getBySel('send-input-phone').blur();
    cy.getBySel('risk-scan-button').should('be.enabled');
    cy.getBySel('risk-scan-button').click();
    cy.wait('@interceptedPhoneKycVerified');
    cy.contains('Verified').should('be.visible');
    cy.getBySel('send-submit-button').should('be.enabled');

    // send token
    submitAndConfirmSend();
    cy.wait('@interceptedTransfer').its('request.body').should('deep.equal', {
      chain_id: 'sepolia',
      contract_address: '******************************************',
      amount: '0.1',
      wallet_address: '0xabaaaaa',
      phone: '+886900000000',
      display_name: 'Bella',
      kyc_status: 'verified',
    });
  });

  it(`Given:
        - Logged in as organization member
      When:
        - Enter a valid address
      Then:
        - Should send token successfully
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept(
      'GET',
      `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/customer?email=verified%40mail.com`,
      {
        statusCode: 200,
        fixture: '/customer/customer-exist-kyc-verified.json',
      },
    ).as('interceptedEmailKycVerified');

    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/transfer`, {
      delay: 100,
      statusCode: 200,
      fixture: '/transfer/transfer.json',
    }).as('interceptedTransfer');

    cy.interceptedTxStatusLoadingThenSuccess();

    // chain, token, amount
    selectBlockchainTokenAndAmount('Sepolia Testnet', 'USDC', '0.01');

    // address
    cy.getBySel('send-type-radio').get('button[value="address"]').click();
    cy.getBySel('send-input-address').type('******************************************');
    cy.getBySel('send-input-address').blur();
    cy.getBySel('risk-scan-button').should('be.enabled');
    cy.getBySel('risk-scan-button').click();
    cy.getBySel('send-submit-button').should('be.enabled');

    // send token
    submitAndConfirmSend();
    cy.wait('@interceptedTransfer').its('request.body').should('deep.equal', {
      chain_id: 'sepolia',
      contract_address: '******************************************',
      amount: '0.01',
      wallet_address: '******************************************',
    });
  });

  it.skip(`Given:
        - Logged in as any member
      When:
        - With remain_limit > 0
        - Enter transfer amount (included valid/invalid amount)
      Then:
        - Should restrict to a maximum of eight decimal places
        - Should display correct value when input amount is a percentage issue in JS (e.g. 0.0331)
        - Should validate input amount and trigger checklist style
      `, () => {
    selectBlockchainTokenAndAmount('Sepolia Testnet', 'USDC', '0.1');
    cy.getBySel('limit-check').contains('Remaining daily transfer limit: $12,345 of $100,000.');
    cy.getBySel('send-input-amount').clear();

    selectBlockchainTokenAndAmount('Sepolia Testnet', 'USDC', '1000');
    cy.getBySel('token-gas-balance-check').contains(
      'Adjust amount or contact admin to deposit at least 978.215192 USDC to ensure the transaction can be made.',
    );
    cy.getBySel('send-input-amount').clear();

    selectBlockchainTokenAndAmount('Sepolia Testnet', 'USDC', '0.123456789');
    cy.getBySel('send-input-amount').should('have.value', '0.12345678');

    // test js percentage calculation
    cy.getBySel('send-input-amount').clear();
    cy.getBySel('send-input-amount').type('0.0331');
    cy.getBySel('send-input-amount').should('have.value', '0.0331');
    cy.getBySel('transaction-note').should('contain.text', '(Optional)');
    cy.getBySel('transaction-attachment-label').should('contain.text', '(Optional)');
    cy.getBySel('send-input-amount').clear();
    cy.getBySel('send-input-amount').type('10101');
    cy.getBySel('approval-threshold-check').should('exist');

    cy.getBySel('transaction-note').should('contain.text', '*');
    cy.getBySel('transaction-attachment').should('contain.text', '*');
  });

  it.skip(`Given:
        - Logged in as organization member
      When:
        - Enter a valid address
      Then:
        - Should fail to send token since transfer limit exceeded
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/transfer`, {
      delay: 100,
      statusCode: 400,
      fixture: '/transfer/transfer-limit-exceeded.json',
    }).as('interceptedTransfer');

    // chain, token, amount
    selectBlockchainTokenAndAmount('Sepolia Testnet', 'USDC', '0.1');

    // address
    cy.getBySel('send-type-radio').get('button[value="address"]').click();
    cy.getBySel('send-input-address').type('******************************************');
    cy.getBySel('send-input-address').blur();
    cy.getBySel('send-submit-button').should('be.enabled');

    // send token
    submitAndConfirmSend();

    cy.wait('@interceptedTransfer').its('request.body').should('deep.equal', {
      chain_id: 'sepolia',
      contract_address: '******************************************',
      amount: '0.1',
      wallet_address: '******************************************',
    });

    cy.contains("Insufficient balance. Please add more funds in your organization's treasury pool.");
    cy.getBySel('transfer-submitted-failed-text').should('contain.text', 'Transaction Failed');
  });

  it.skip(`Given:
        - Logged in as organization member
      When:
        - Enter a valid address
      Then:
        - Should fail to send token since execution revert: transfer amount exceeds
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/transfer`, {
      delay: 100,
      statusCode: 500,
      fixture: '/transfer/transfer-execution-revert.json',
    }).as('interceptedTransfer');

    // chain, token, amount
    selectBlockchainTokenAndAmount('Sepolia Testnet', 'USDC', '0.1');

    // address
    cy.getBySel('send-type-radio').get('button[value="address"]').click();
    cy.getBySel('send-input-address').type('******************************************');
    cy.getBySel('send-input-address').blur();
    cy.getBySel('send-submit-button').should('be.enabled');

    // send token
    submitAndConfirmSend();

    cy.wait('@interceptedTransfer').its('request.body').should('deep.equal', {
      chain_id: 'sepolia',
      contract_address: '******************************************',
      amount: '0.1',
      wallet_address: '******************************************',
    });

    cy.contains("Insufficient balance. Please add more funds in your organization's treasury pool.");
  });
});

describe('[UI] Send Token Page - when remain limit is zero ', () => {
  beforeEach(() => {
    cy.interceptedGetTokens();
    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/accounts`, {
      fixture: '/treasury/accounts.json',
    }).as('getKGAccounts');
    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/asset_prices*`, {
      fixture: '/treasury/price.json',
    }).as('getPrice');
  });

  it(`Given:
        - Logged in as not AssetPro-Admin/Owner
      When:
        - With daily transfer limit is not set
      Then:
        - Should print correct checklist description
      `, () => {
    cy.setLoggedIn({
      roles: {
        asset_pro: ['asset_pro:approver', 'asset_pro:trader'],
      },
      asset_pro: {
        remain_limit: 0,
        daily_transfer_limit: 10,
        transfer_approval_threshold: 20000.0,
      },
      permissions: [
        {
          resource: 'transaction',
          action: 'apply',
        },
      ],
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
    });
    cy.visit('/en-US/asset/transfer');
    cy.wait('@getPrice');

    cy.getBySel('limit-check').contains('Remaining daily transfer limit: $0 of $10.').should('be.visible');
  });

  it(`Given:
        - Logged in as AssetPro AssetPro-Admin/Owner member
      When:
        - With remain_limit = 0
      Then:
        - Should print correct checklist description with a button to edit transfer limit
      `, () => {
    cy.setLoggedIn({
      roles: {
        asset_pro: ['asset_pro:admin', 'asset_pro:approver', 'asset_pro:trader'],
        admin: ['owner'],
      },
      asset_pro: {
        remain_limit: 0,
        daily_transfer_limit: 10,
        transfer_approval_threshold: 20000.0,
      },
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
    });
    cy.visit('/en-US/asset/transfer');
    cy.wait('@getPrice');
    selectBlockchainTokenAndAmount('Sepolia Testnet', 'USDC', '100000');
    cy.getBySel('limit-check').find('p').contains('Adjust amount or contact Admins to adjust your transfer limit. ');
    cy.getBySel('limit-check').find('button').should('exist');
  });
});
