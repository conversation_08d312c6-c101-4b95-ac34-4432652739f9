{"common.action": "行动", "common.actions": "行动", "common.add": "添加", "common.admin": "管理员", "common.amount": "數量", "common.anonymous": "匿名", "common.any-chain": "", "common.any-token": "任何代币", "common.blockchain": "区块链", "common.cancel": "取消", "common.checking": "正在检查", "common.clear-all": "全部清除", "common.confirm": "确认", "common.confirmation": "确认", "common.connect-wallet-to-login": "请连接您的钱包进行登入", "common.contact-us": "联系我们", "common.copy": "复制", "common.created-at": "创建于", "common.delete": "删除", "common.edit": "编辑", "common.email": "电子邮件", "common.enter-email": "输入电子邮件", "common.enter-phone-number": "输入电话号码", "common.error": "错误", "common.go-back": "回去", "common.hide-filter": "隐藏筛选器", "common.learn-more-about": "了解更多关于", "common.loading": "正在加载", "common.member": "成员", "common.name": "姓名", "common.no-access": "无访问权限", "common.no-data-available": "无可用数据", "common.no-image": "", "common.owner": "所有者", "common.owners": "所有者", "common.phone-number": "电话号码", "common.pick-date": "选择一个日期", "common.please-check": "请检查", "common.please-wait": "请稍候", "common.privacy-policy": "隐私政策", "common.recipient": "收款人", "common.refresh": "刷新", "common.remove": "移除", "common.role": "权限", "common.save": "保存", "common.search-placeholder": "快速搜寻", "common.select": "选择", "common.select-chain": "选择链", "common.select-status": "选择状态", "common.select-token": "选择代币", "common.send": "发送", "common.send-token": "发送代币", "common.show-filter": "显示筛选器", "common.status.active": "活跃", "common.status.expired": "已过期", "common.status.inactive": "已停用", "common.status.pending": "待定", "common.status.text": "状态", "common.status.title": "状态", "common.terms": "条款", "common.time": "时间", "common.time-utc8": "时间（世界标准时间+8）", "common.token": "代币", "common.tx-hash": "交易序号", "common.tx-status-failed": "失败了", "common.tx-status-pending": "处理中", "common.tx-status-success": "成功", "common.update": "更新", "common.user": "用户", "common.wallet": "钱包生成器", "common.wallet-address": "钱包地址", "common.welcome": "欢迎！", "error.cant-find-user": "对不起，我们找不到此用户。", "error.general-error": "发生了一些错误。代码： ", "error.no-access": "你没有访问权限。请使用经过验证的钱包地址登录。", "error.try-again": "出现了一些错误，请稍后再试。", "error.user-not-found": "未找到用户。", "kgauth.change.change-email": "更改邮箱", "kgauth.change.change-phone-number": "更改电话号码", "kgauth.change.email-different": "新电子邮件必须与旧电子邮件不同", "kgauth.change.email-exists": "电子邮件已经存在", "kgauth.change.email-update-failed": "更新电子邮件失败。请稍后再试。", "kgauth.change.new-email": "新邮件", "kgauth.change.new-phone-number": "新电话号码", "kgauth.change.old-email": "你的旧电子邮件是", "kgauth.change.old-phone-number": "你的旧电话号码是", "kgauth.change.password-input": "密码", "kgauth.change.password-input2": "确认密码", "kgauth.change.password-input-hint1": "至少 12 位数", "kgauth.change.password-input-hint2": "包含大写和小写、数字和字母", "kgauth.change.password-mismatch": "密码必须匹配", "kgauth.change.password-title": "更改密码", "kgauth.change.phone-duplicate": "新电话号码必须与旧电话号码不同", "kgauth.change.phone-exists": "电话号码已经存在", "kgauth.change.verify-email": "验证电子邮件", "kgauth.change.verify-phone-number": "验证电话号码", "kgauth.common.accept": "接受", "kgauth.common.change-email-address": "更改电子邮件地址", "kgauth.common.change-password": "更改密码", "kgauth.common.change-phone-number": "更改电话号码", "kgauth.common.continue": "下一步", "kgauth.common.decline": "拒绝", "kgauth.common.done": "完成了", "kgauth.common.link-line-account": "关联 LINE 账户", "kgauth.common.loading": "数据处理正在进行中。请不要关闭此窗口。", "kgauth.common.login": "登录", "kgauth.common.oauth-login": "oauth 登录", "kgauth.common.resend": "重新发送", "kgauth.common.retry": "重试", "kgauth.common.seconds": "秒", "kgauth.common.verify-email-address": "验证电子邮件地址", "kgauth.common.verify-phone-number": "验证电话号码", "kgauth.errors.common": "出了点问题！", "kgauth.errors.common-retry": "出了点问题。请稍后再试。", "kgauth.errors.invalid-email": "无效的电子邮件", "kgauth.errors.invalid-otp": "无效的代码。请再试一次。", "kgauth.errors.invalid-phone": "无效的电话号码", "kgauth.errors.otp-digit": "OTP 必须是 6 位数字", "kgauth.errors.password-required": "密码错误。", "kgauth.errors.rate-limit": "已超过速率限制。请稍后再试。", "kgauth.errors.return-to-wallet-in": "回到钱包里", "kgauth.errors.return-to-wallet-now": "立即返回钱包", "kgauth.errors.token-expired": "登入已过期。请重新登入以继续。", "kgauth.errors.update-phone": "更新电话号码失败。请稍后再试。", "kgauth.forgot.backup-seed": "备用助记词", "kgauth.forgot.desc": "重要：如果您单击 “确定” 重置密码，我们将同时删除您账户的资产信息。别担心，您可以稍后通过助记词导入资产。但是我们建议您先备份助记词。", "kgauth.forgot.password": "忘记密码", "kgauth.forgot.reset": "登出并重置密码。", "kgauth.forgot.step1": "注销应用程序", "kgauth.forgot.step2": "再次登录", "kgauth.forgot.step3": "点击 “忘记密码” 并按照指南进行操作", "kgauth.forgot.step-title": "请单击 “确定” 并按照步骤操作", "kgauth.forgot.sub-title": "请先登出，以便我们帮助您重置密码。", "kgauth.login.email": "电子邮件", "kgauth.login.input-required": "登录/注册信息为必填项。", "kgauth.login.or": "要么", "kgauth.login.phone": "电话", "kgauth.login.signin-sub-title": "以继续使用", "kgauth.login.signin-title": "使用 KryptoGO 登录/注册", "kgauth.login.with-google": "用谷歌登录", "kgauth.oauth-callback.authorize-description": "请求以下权限。请查看并确保您同意与本网站或应用程序共享此敏感信息", "kgauth.oauth-callback.authorize-item-0": "查看您的 KryptoGO 账户信息，包括您的电子邮件、电话号码和用户名称", "kgauth.oauth-callback.authorize-item-1": "访问您的钱包地址并查看您的数字资产", "kgauth.oauth-callback.authorize-item-2": "查看您的交易历史", "kgauth.oauth-callback.authorize-item-3": "", "kgauth.oauth-callback.authorize-scope-and": "和", "kgauth.oauth-callback.authorize-scope-asset": "资产、NFT、交易", "kgauth.oauth-callback.authorize-scope-chatroom": "聊天室", "kgauth.oauth-callback.authorize-scope-edit": "编辑", "kgauth.oauth-callback.authorize-scope-notification": "通知", "kgauth.oauth-callback.authorize-scope-order": "订单", "kgauth.oauth-callback.authorize-scope-read": "观点", "kgauth.oauth-callback.authorize-scope-token": "代币", "kgauth.oauth-callback.authorize-scope-transaction": "交易", "kgauth.oauth-callback.authorize-scope-user": "用户信息", "kgauth.oauth-callback.authorize-scope-vault": "保险库", "kgauth.oauth-callback.authorize-scope-wallet": "钱包", "kgauth.oauth-callback.authorize-scope-your": "您的", "kgauth.oauth-callback.subtitle": "应用程序想要访问您的 KryptoGO 账户", "kgauth.oauth-callback.title": "授权应用程序", "kgauth.password.enter-password": "输入你的密码", "kgauth.password.forget-password": "忘记密码？", "kgauth.password.invalid": "密码无效。请再试一次。", "kgauth.password.sub-title": "要访问您的钱包资产，请使用您的密码恢复钱包。", "kgauth.success.email": "电子邮件已成功验证！", "kgauth.success.email-updated": "电子邮件已成功更新！", "kgauth.success.password-updated": "密码更新成功！", "kgauth.success.phone": "电话号码已成功验证！", "kgauth.success.phone-updated": "手机号码更新成功！", "kgauth.verify.enter-digit": "将您收到的 6 位数代码输入到", "kgauth.verify.not-receive-code": "没有收到验证码？", "kgauth.verify.title": "我们已经发送了验证码至", "kgform.common.back-to-home": "返回首页", "kgform.common.change": "更改", "kgform.common.next-page": "下一页", "kgform.common.previous-page": "上一页", "kgform.common.resend": "重新发送", "kgform.common.start": "开始", "kgform.common.submit": "提交", "kgform.common.verify": "验证", "kgform.errors.addres.in-use": "该地址已被使用", "kgform.errors.address.in-use": "该地址已在使用中。", "kgform.errors.address.invalid-format": "钱包地址格式不正确，请再次确认", "kgform.errors.birth-date": "您必须至少18岁", "kgform.errors.code.invalid": "验证码不正确", "kgform.errors.code.invalid-format": "验证码必须为6位数字", "kgform.errors.email.in-use": "该电子邮件已被使用", "kgform.errors.email.invalid-format": "无效的电子邮件格式", "kgform.errors.file-upload.file-too-large": "档案过大", "kgform.errors.form-validation-error": "部分字段填写不正确。", "kgform.errors.idIssue-date": "请提供完整的发证日期", "kgform.errors.id-number": "证件号码格式不正确，请再次确认", "kgform.errors.max-length": "最多为 {maxLength} 个字", "kgform.errors.no-client-id": "未获得有效 OAuth 客户端 ID", "kgform.errors.oauth-login-error": "登录失败。", "kgform.errors.phone.in-use": "这个行动电话已经被使用", "kgform.errors.phone.invalid-format": "请输入有效的手机号码", "kgform.errors.privacy-policy": "请同意个人资料安全与隐私权政策", "kgform.errors.required": "此栏位为必填栏位", "kgform.errors.signature.empty": "请签全名", "kgform.errors.signature.invalid": "签名失败", "kgform.errors.something-went-wrong": "出现问题，请稍后再试。", "kgform.errors.submission-error": "提交错误。请稍后再试。", "kgform.errors.too-many-request": "请求过多。请稍后再试。", "kgform.errros.code.invalid": "验证码不正确", "kgform.form.address.connect": "连结钱包", "kgform.form.address.disconnect": "解除连结", "kgform.form.email.dialog.description": "验证码已发送至 {email}", "kgform.form.email.dialog.title": "电子邮件验证", "kgform.form.email.in-use": "该电子邮件已被使用", "kgform.form.file-upload.choose-file": "选择档案", "kgform.form.file-upload.start-over": "重新选择", "kgform.form.login-and-start": "登录并开始", "kgform.form.phone.dialog.description": "验证码已传送到 {phone}", "kgform.form.phone.dialog.title": "验证行动电话", "kgform.form.signature.clear": "清除", "kgform.form.signature.dialog.title": "签名", "kgform.form.signature.placeholder": "点击此处签署您的全名", "kgform.form.signature.save": "保存", "kgform.form.successfully-verirfied": "验证成功。", "kgform.form.upload-placeholder": "点击或拖拽上传文件", "kgform.index.buy-crypto": "买币", "kgform.index.login": "登录", "kgform.index.register": "注册", "kgstore.checkout.cta": "我已经付款", "kgstore.checkout.customer-info": "客户信息", "kgstore.checkout.error-create-order": "创建订单失败。", "kgstore.checkout.order-correct-text": "创建订单后，您可以在指定的时限内付款。", "kgstore.checkout.order-correct-title": "订单正确吗？", "kgstore.checkout.order-summary": "订单摘要", "kgstore.checkout.rate-updated-text": "您选择的商品已有新费率。请再试一次。", "kgstore.checkout.rate-updated-title": "费率已更新", "kgstore.checkout.receiving-wallet": "收款钱包", "kgstore.checkout.title": "结账", "kgstore.checkout.toast": "您的付款截止日期将在确认订单之后{time}。", "kgstore.checkout.transfer-fund": "将资金转移到 ", "kgstore.checkout.will-receive": "你会收到", "kgstore.common.about": "关于", "kgstore.common.back": "返回", "kgstore.common.bank-transfer": "银行转账", "kgstore.common.buy": "购买", "kgstore.common.cancel": "取消", "kgstore.common.confirm": "确认", "kgstore.common.copy-success": "复制成功！", "kgstore.common.email": "电子邮件", "kgstore.common.error": "出了点问题。请稍后再试。", "kgstore.common.error-get-wallet": "无法获取钱包信息。", "kgstore.common.kyc-status.pending": "待处理", "kgstore.common.kyc-status.processing": "处理中", "kgstore.common.kyc-status.rejected": "已拒绝", "kgstore.common.kyc-status.unverified": "未证实", "kgstore.common.kyc-status.verified": "已验证", "kgstore.common.limit": "极限", "kgstore.common.login-now": "立即登录", "kgstore.common.login-order-desc": "登录下订单", "kgstore.common.login-order-title": "登录下订单", "kgstore.common.next": "下一步", "kgstore.common.payment-method": "付款方式", "kgstore.common.phone": "电话", "kgstore.common.rate": "费率", "kgstore.common.recaptcha-error": "无法验证谷歌 reCAPTCHA。请刷新网页或联系我们的支持团队寻求帮助。", "kgstore.common.submit": "提交", "kgstore.common.subtotal": "小计", "kgstore.common.total": "总计", "kgstore.common.try-again": "再试一次", "kgstore.login.back": "返回", "kgstore.login.check-email-code": "请查看您的电子邮件以接收 OTP 代码。", "kgstore.login.continue": "下一步", "kgstore.login.email-required": "电子邮件为必填项", "kgstore.login.otp-message": "OTP 必须是 6 位数字", "kgstore.login.phone-required": "电话号码为必填项", "kgstore.login.resend": "重新发送", "kgstore.login.success": "登录成功。 正在重定向，请稍候。", "kgstore.login.title": "登录/注册 KryptoGO 继续", "kgstore.login.welcome": "欢迎！", "kgstore.menu.orders": "订单", "kgstore.menu.product": "产品", "kgstore.menu.profile": "概况", "kgstore.order.active-title": "活跃", "kgstore.order.attachments.count": "{count}档案", "kgstore.order.attachments.title": "附件", "kgstore.order.cancelled-time": "取消时间", "kgstore.order.cancel-toast": "所有者取消了订单{orderId}，总付款为{cost}。\n\n如果您已经付款，请尽快联系店主以获得退款。", "kgstore.order.cancel-toast-cta": "查看联系信息", "kgstore.order.delivered-time": "交付时间", "kgstore.order.error-get-order": "无法检索在售订单", "kgstore.order.error-get-order-details": "无法检索订单详情", "kgstore.order.history-title": "历史", "kgstore.order.no-order": "您还没有進行中或已完成的订单", "kgstore.order.no-order-history": "尚無已完成的订单。", "kgstore.order.order-created": "订单已创建", "kgstore.order.payment-time": "付款时间", "kgstore.order.receiving-wallet": "收款钱包", "kgstore.order.shipment-time": "发货时间", "kgstore.order.shipment-tx-hash": "交易哈希", "kgstore.order.shipping-time": "配送时间", "kgstore.order.status-awaiting-confirmation": "等待确认", "kgstore.order.status-awaiting-shipment": "等待发货", "kgstore.order.status-canceled": "已取消", "kgstore.order.status-delivered": "已交付", "kgstore.order.status-shipping": "运输中", "kgstore.order.status-unpaid": "未付款", "kgstore.order.unpaid-toast": "请事{order}先付款{time}。", "kgstore.order.view-more": "查看更多", "kgstore.order.view-order-detail": "查看订单详情", "kgstore.order.view-receipt": "查看收据", "kgstore.payment.desc": "请上传至少 1 张屏幕截图、图片或任何其他附件以证明您的付款。", "kgstore.payment.error-attachment": "上传附件失败", "kgstore.payment.error-file-size": "文件大小太大，无法上传。", "kgstore.payment.error-file-type": "文件类型无效。只允许使用 png、jpg、jpeg 和 webp。", "kgstore.payment.error-max-upload": "只允许 10 个附件。", "kgstore.payment.error-upload": "上传付款详情失败", "kgstore.payment.file-restrict": "将 png/jpg/jpeg/heic/heif 放在这里\n文件大小限制为 10MB\n图像比例 300*8000px", "kgstore.payment.status-awaiting-refund": "等待退款", "kgstore.payment.status-paid": "已付费", "kgstore.payment.status-refunded": "已退款", "kgstore.payment.success-upload": "付款详情上传成功！", "kgstore.payment.title": "上传附件", "kgstore.payment.upload-and-inform": "上传并通知卖家", "kgstore.payment.x-files-uploaded": "文件已上传", "kgstore.product.account-name": "账户名", "kgstore.product.account-number": "账号", "kgstore.product.bank-name": "银行名称", "kgstore.product.branch-name": "分支", "kgstore.product.buy-now": "立即购买", "kgstore.product.cta-need-kyc": "申请 KYC 继续", "kgstore.product.cta-sign-in": "登录购买", "kgstore.product.current-rate": "当前汇率", "kgstore.product.error-max-amount": "最大金额为", "kgstore.product.error-max-quantity": "最大数量为", "kgstore.product.error-merchant": "检索卖家信息失败。", "kgstore.product.error-min-amount": "最低金额为", "kgstore.product.error-min-quantity": "最小数量是", "kgstore.product.fee-desc-1": "总价格包括所有费用。实际购买数量将计算为", "kgstore.product.fee-desc-2": "如果您的购买数量的手续费低于最低金额，则总价格将相应调整。", "kgstore.product.fee-formula-1": "总额/汇率。", "kgstore.product.fee-formula-2": "总计/（汇率*（1+比例费用）。", "kgstore.product.handling-fee": "手续费", "kgstore.product.include-fee": "价格包含{fee_percentage}% 手续费", "kgstore.product.introduction": "导言", "kgstore.product.limit": "极限", "kgstore.product.market-profile": "市场概况", "kgstore.product.minimum-handling-fee": "最低手续费", "kgstore.product.no-handling-fee": "无手续费", "kgstore.product.no-product": "所有者没有发布任何产品。", "kgstore.product.receive": "并接收", "kgstore.product.spend": "我想花", "kgstore.product.title": "产品", "kgstore.product.transfer-funds": "将资金转移到", "kgstore.profile.title": "概况", "kgstore.toast.guest-desc": "立即登录并验证您的身份，确保您可以随时看到自己喜欢的产品下订单！", "kgstore.toast.guest-title": "享受最安全的加密交易", "kgstore.toast.pending-title": "您的身份申请正在审核中。", "kgstore.toast.rejected-desc": "请联系我们的客户支持以了解有关详细信息的更多信息。", "kgstore.toast.rejected-title": "身份申请已被拒绝。", "kgstore.toast.unverified-title": "验证您的身份即可开始交易。", "kgstore.toast.verified-desc": "您的身份申请已通过验证！您现在可以下订单。", "kgstore.toast.verified-title": "身份验证成功！", "kgstore.unpaid.cta": "我已经付款", "kgstudio.asset.amount-invalid-description-1": "调整金额或联系管理员至少存款", "kgstudio.asset.amount-invalid-description-2": "和", "kgstudio.asset.amount-invalid-description-3": "确保可以进行交易。", "kgstudio.asset.available-balance": "可用余额： ", "kgstudio.asset.balance": "资产余额", "kgstudio.asset.balance-checking": "余额检查...", "kgstudio.asset.balance-value": "余额: {formattedTokenBalance}", "kgstudio.asset.checking-kya": "检查钱包地址的潜在风险", "kgstudio.asset.checking-kyc": "检查 KYC 状态", "kgstudio.asset.checklist.contact-hint": "今天的剩余余额：$0（每日转账限额：$0）您的系统管理员或所有者似乎没有设置您的转账限额。请联系他们以帮助您设置转账限额。", "kgstudio.asset.checklist.edit-hint": "编辑您的转账限额", "kgstudio.asset.checklist.exceed-hint": "超过可以转出的金额，请减少金额。（今天的剩余额度：${remainLimit}）", "kgstudio.asset.checklist.remain-hint": "今天的剩余余额：${remainLimit}（每日转账限额：${dailyTransferLimit}）", "kgstudio.asset.checklist.reuqired-approval": "这笔交易需要审查。（批准门槛：美元{threshold}）", "kgstudio.asset.checklist.reuqired-hint": "金额应大于 0。（今天的剩余余额：${remainLimit}）", "kgstudio.asset.check-tx": "请查看交易", "kgstudio.asset.customer-transfer-time": "客户付款通知", "kgstudio.asset.deposit-now": "立即存款", "kgstudio.asset.edit-now": "立即编辑", "kgstudio.asset.edit-order-modal.accepted-file-types": "仅接受文件类型：png、jpg、jpeg、webp", "kgstudio.asset.edit-order-modal.cancel-transaction-instruction": "如果您想停止交易，请取消此订单。", "kgstudio.asset.edit-order-modal.change-status-to": "将状态更改为", "kgstudio.asset.edit-order-modal.edit-unpaid-himt": "请根据客户提供的付款证明填写以下信息", "kgstudio.asset.edit-order-modal.file-size-error": "文件大小超过 10MB", "kgstudio.asset.edit-order-modal.max-files-error": "已超过最大文件数", "kgstudio.asset.edit-order-modal.max-file-upload-info": "最多可以上传 10 个文件，每个文件大小不超过 1MB", "kgstudio.asset.edit-order-modal.payment-amount-mismatch": "实际付款金额与订单金额不同", "kgstudio.asset.edit-order-modal.payment-done-hint": "买家已完成付款；请立即安排发货。如果您不接受这笔交易，则必须在取消订单后立即处理退款。", "kgstudio.asset.edit-order-modal.payment-note": "付款凭证", "kgstudio.asset.edit-order-modal.title": "编辑付款详情", "kgstudio.asset.edit-order-modal.upload-attachments": "上传附件", "kgstudio.asset.edit-tx-note-modal.attachments.title": "附件", "kgstudio.asset.edit-tx-note-modal.note.hint": "输入交易备注并上传必要的附件以供批准。", "kgstudio.asset.edit-tx-note-modal.note.placeholder": "本次交易注意事项", "kgstudio.asset.edit-tx-note-modal.note.title": "交易须知", "kgstudio.asset.edit-tx-note-modal.title": "编辑", "kgstudio.asset.edit-tx-note-modal.update-failed-error": "翻译占位符", "kgstudio.asset.estimated-gas": "预计燃料费：", "kgstudio.asset.estimated-gas-checking": "预计手續费：正在检查...", "kgstudio.asset.finance.loading": "正在生成仪表板数据...", "kgstudio.asset.gas-insufficient": "主币余额不足。", "kgstudio.asset.kya-info.total-balance": "资产余额", "kgstudio.asset.kya-info.total-received": "收到的总数", "kgstudio.asset.kya-info.total-spent": "总支出", "kgstudio.asset.kya-info.total-transactions": "交易总数", "kgstudio.asset.kya-status.atm.desc": "可提领加密货币的ATM机台", "kgstudio.asset.kya-status.atm.name": "实体ATM", "kgstudio.asset.kya-status.child-exploitation.desc": "无更多介绍", "kgstudio.asset.kya-status.child-exploitation.name": "剥削童工", "kgstudio.asset.kya-status.dark-market.desc": "透过暗网提供非法产品并获取的加密货币。", "kgstudio.asset.kya-status.dark-market.name": "暗网市集", "kgstudio.asset.kya-status.dark-service.desc": "透过暗网提供非法服务并获取的加密货币。", "kgstudio.asset.kya-status.dark-service.name": "暗网服务", "kgstudio.asset.kya-status.enforcement-action.desc": "该实体正面临法律诉讼。其管辖区将作为子类别进行注释。", "kgstudio.asset.kya-status.enforcement-action.name": "有法律诉讼争议", "'kgstudio.asset.kya-status.error'": "目前无法对该地址执行风险扫描，请稍后重试。", "kgstudio.asset.kya-status.error": "目前无法针对此地址进行风险扫描，请稍后再试", "kgstudio.asset.kya-status.exchange-fraudulent.desc": "曾涉及非法活动的交易所。", "kgstudio.asset.kya-status.exchange-fraudulent.name": "交易所：涉及非法活动", "kgstudio.asset.kya-status.exchange-licensed.desc": "持有加密资产业务许可证的服务，包括保管、交易、经纪或其他相关的金融服务。提供一种交易服务，参与者与中心方（该实体）互动。不包括非特定的金融服务许可证和被列为不合作的FATF管辖区。", "kgstudio.asset.kya-status.exchange-licensed.name": "交易所：合法牌照", "kgstudio.asset.kya-status.exchange-unlicensed.desc": "该服务未持有特定加密资产商业许可证，提供一种交易服务，参与者与中心方（该实体）进行交易。包含在被FATF列为不合作的司法管辖区的有许可证的实体。", "kgstudio.asset.kya-status.exchange-unlicensed.name": "交易所：无牌照", "kgstudio.asset.kya-status.gambling.desc": "使用加密货币的博弈。", "kgstudio.asset.kya-status.gambling.name": "线上赌博", "kgstudio.asset.kya-status.high": "高", "kgstudio.asset.kya-status.high-risk": "高风险", "kgstudio.asset.kya-status.illegal-service.desc": "涉及或提供非法活动的资金。", "kgstudio.asset.kya-status.illegal-service.name": "非法活动", "kgstudio.asset.kya-status.liquidity-pools.desc": "无更多介绍", "kgstudio.asset.kya-status.liquidity-pools.name": "流动性资金池", "kgstudio.asset.kya-status.low": "低", "kgstudio.asset.kya-status.low-risk": "低风险", "kgstudio.asset.kya-status.marketplace.desc": "提供合法加密货币交易的市集。", "kgstudio.asset.kya-status.marketplace.name": "合法市集", "kgstudio.asset.kya-status.medium": "中", "kgstudio.asset.kya-status.medium-risk": "中等风险", "kgstudio.asset.kya-status.miner.desc": "利用电脑算力自区块链挖矿的所得。", "kgstudio.asset.kya-status.miner.name": "挖矿", "kgstudio.asset.kya-status.mixer.desc": "将资金从不同来源混合，使其追踪变得困难或几乎不可能的服务。它主要用于洗钱。", "kgstudio.asset.kya-status.mixer.name": "难以溯源的资金", "kgstudio.asset.kya-status.not-enough-info": "该地址没有足够的信息来进行风险分析。", "kgstudio.asset.kya-status.other.desc": "没有其他信息。", "kgstudio.asset.kya-status.other.name": "其他可信来源", "kgstudio.asset.kya-status.others.desc": "无法归类为特定类型资金", "kgstudio.asset.kya-status.others.name": "其他", "kgstudio.asset.kya-status.p2p-exchange-licensed.desc": "该服务持有特定于加密资产的商业许可证，包括保管、交易、经纪或其他相关的金融服务。它提供一种交易服务，参与者可以直接与彼此进行交易。它并不包括非特定的金融服务许可证和被列为与金融行动特别工作组（FATF）不合作的国家和地区。", "kgstudio.asset.kya-status.p2p-exchange-licensed.name": "P2P交易：合法牌照", "kgstudio.asset.kya-status.p2p-exchange-unlicensed.desc": "该服务并未持有任何特定于加密资产的商业许可证，而是提供一种交易服务，让参与者能直接进行交易。此包含在被金融行动特别工作组（FATF）列为非合作的司法管辖区内拥有许可证的实体。", "kgstudio.asset.kya-status.p2p-exchange-unlicensed.name": "P2P交易：无牌照", "kgstudio.asset.kya-status.payment.desc": "一种作为客户与提供付款服务的公司之间中介的服务。", "kgstudio.asset.kya-status.payment.name": "一般支付", "kgstudio.asset.kya-status.potential-risk": "地址的潜在风险", "kgstudio.asset.kya-status.ransom.desc": "加密货币形式的勒索赎金。", "kgstudio.asset.kya-status.ransom.name": "勒索赎金", "kgstudio.asset.kya-status.sanctions.desc": "无更多介绍", "kgstudio.asset.kya-status.sanctions.name": "制裁", "kgstudio.asset.kya-status.scam.desc": "以诈骗手法而取得的加密货币。", "kgstudio.asset.kya-status.scam.name": "诈骗所得", "kgstudio.asset.kya-status.seized-assets.desc": "无更多介绍", "kgstudio.asset.kya-status.seized-assets.name": "主管机关扣押资产", "kgstudio.asset.kya-status.source.suspicious": "可疑来源", "kgstudio.asset.kya-status.source.trusted": "可信来源", "kgstudio.asset.kya-status.stolen-coins.desc": "以黑客攻击窃取他人的加密货币。", "kgstudio.asset.kya-status.stolen-coins.name": "他人赃款", "kgstudio.asset.kya-status.terrorist-financing.desc": "无更多介绍", "kgstudio.asset.kya-status.terrorist-financing.name": "资助恐怖主义", "kgstudio.asset.kya-status.view-potential-risk-details": "查看潜在风险详情", "kgstudio.asset.kya-status.wallet-address-risk": "钱包地址风险", "kgstudio.asset.kya-status.wallet.desc": "用以储存或支付加密货币的钱包。", "kgstudio.asset.kya-status.wallet.name": "数字钱包", "kgstudio.asset.kya-status.wallet-risk-checkin": "正在分析钱包风险...", "kgstudio.asset.kyc-status.scan": "风险扫描", "kgstudio.asset.kyc-status.scan-address": "地址风险", "kgstudio.asset.kyc-status.subtitle": "在转出之前，请检查用户信息。", "kgstudio.asset.market-profile.email": "电子邮件", "kgstudio.asset.market-profile.intro": "导言", "kgstudio.asset.market-profile.intro-tooltip": "显示在商店的顶部。", "kgstudio.asset.market-profile.line-id": "线路 ID", "kgstudio.asset.market-profile.logo": "徽标", "kgstudio.asset.market-profile.phone": "电话", "kgstudio.asset.market-profile.section-title": "市场概况", "kgstudio.asset.market-profile.store-link": "预览", "kgstudio.asset.market-profile.title": "标题", "kgstudio.asset.market-profile.title-tooltip": "显示在网站标题中。", "kgstudio.asset.market-profile.url": "网址", "kgstudio.asset.market-profile.url-tooltip": "要自定义您的域名，请联系客服。", "kgstudio.asset.mismatch-error": "该用户的 app 尚未从所选链中导入钱包 ({selectedBlockchain})。他们可能看不到你寄给他们的钱。", "kgstudio.asset.note-attachments-required": "输入交易备注并上传必要的附件以供批准。如果您想在未经批准的情况下发货，请調降金额或编辑您的限额。", "kgstudio.asset.order-detail.action-card.remain-balance": "今天的剩余余额：{balance}（每日转账限额：{limit}）", "kgstudio.asset.order-detail.action-card.tx-failed": "交易{txHash}发送失败。请再试一次。({shippedAt})", "kgstudio.asset.order-detail.actions.awaiting-confirmation": "买家已将付款通知我们，请尽快查看。", "kgstudio.asset.order-detail.actions.cancel-order": "取消订单", "kgstudio.asset.order-detail.actions.confirmation-hint": "为防止洗钱，请验证用户注册的银行账户是否与汇款账户相匹配，以确保这是他们的交易。否则，您可以取消并退款。", "kgstudio.asset.order-detail.actions.mark-as-paid": "标记为已付款", "kgstudio.asset.order-detail.actions.order-cancelled": "订单已取消。", "kgstudio.asset.order-detail.actions.order-done": "订单已完成。", "kgstudio.asset.order-detail.actions.order-shipping": "目前正在运输中。请通过发货证明查看实时发货进度。", "kgstudio.asset.order-detail.actions.payment-deadline": "客户应在{deadline}前完成付款，否则您可以取消此订单。\n\n在客户付款之前，如果您不接受交易，则可以在不处理退款的情况下取消订单。", "kgstudio.asset.order-detail.actions.title": "行动", "kgstudio.asset.order-detail.cancel-modal.cancel": "取消此订单", "kgstudio.asset.order-detail.cancel-modal.description": "请在内部备注中填写原因。用户已完成付款。取消此订单后，您需要尽快处理退款。", "kgstudio.asset.order-detail.cancel-modal.title": "你确定要取消这个订单吗？", "kgstudio.asset.order-detail.edit-internal-note": "编辑内部笔记", "kgstudio.asset.order-detail.order-details": "订单详情", "kgstudio.asset.order-detail.order-information": "订单信息", "kgstudio.asset.order-detail.order-information.customer": "顾客", "kgstudio.asset.order-detail.order-information.order-time": "下单时间", "kgstudio.asset.order-detail.order-information.payment-details": "付款详情", "kgstudio.asset.order-detail.order-information.product": "产品", "kgstudio.asset.order-detail.order-information.qty": "数量", "kgstudio.asset.order-detail.order-information.title": "订单信息", "kgstudio.asset.order-detail.order-information.total-price": "总价格", "kgstudio.asset.order-detail.order-information.tx-id": "订单编号", "kgstudio.asset.order-detail.payment-details.account-info": "银行名称：{bankName}\n分支机构名称：{branchName}\n账号：{accountNumber}\n账户持有人姓名：{accountHolderName}", "kgstudio.asset.order-detail.payment-details.account-info-less-content": "{bankName}{branchName}\n账号：{accountNumber}\n账户持有人姓名：{accountHolderName}", "kgstudio.asset.order-detail.payment-details.confirmed-as-paid": "此订单已确认：已付款。", "kgstudio.asset.order-detail.payment-details.customer-account": "用户注册的个人银行账户信息（有关更多信息，请参阅用户 KYC 详细信息）：", "kgstudio.asset.order-detail.payment-details.info-from-customer": "来自客户的付款信息", "kgstudio.asset.order-detail.payment-details.last-five-digits": "实际付款账号的最后五位数", "kgstudio.asset.order-detail.payment-details.title": "付款详情", "kgstudio.asset.order-detail.payment-details.unpaid": "客户尚未通知付款。", "kgstudio.asset.order-detail.payment-details.unpaid-time-reminder": "付款截止日期是{deadline}，在付款截止日期之前还剩{timeLeft}。", "kgstudio.asset.order-detail.summary.internal-note": "内部笔记", "kgstudio.asset.order-detail.summary.internal-note-hint": "填写订单处理状态、取消订单的原因、退款信息和进度等详细信息。", "kgstudio.asset.order-detail.summary.payment-status": "付款状态", "kgstudio.asset.order-detail.summary.process-by": "处理者", "kgstudio.asset.order-detail.summary.shipment-status": "发货状态", "kgstudio.asset.order-detail.summary.shipping-proof": "发货证明", "kgstudio.asset.order-detail.summary.title": "摘要", "kgstudio.asset.order-settings.payment-terms": "付款条款", "kgstudio.asset.order-settings.payment-terms-tooltip": "订单建立后，将自动显示付款截止日期提醒。您仍然需要自己取消过期的订单。", "kgstudio.asset.order-settings.section-title": "订单设置", "kgstudio.asset.orders.just now": "刚才", "kgstudio.asset.orders.just-now": "刚才", "kgstudio.asset.orders.list.customer": "顾客", "kgstudio.asset.orders.list.order-created": "订单已创建", "kgstudio.asset.orders.list.order-id": "订单编号", "kgstudio.asset.orders.list.order-purchase": "购买", "kgstudio.asset.orders.list.order-status": "订单状态", "kgstudio.asset.orders.list.payment": "付款", "kgstudio.asset.orders.list.shipment": "运输", "kgstudio.asset.orders.list.total-price": "总价格", "kgstudio.asset.orders.search.placeholder": "订单编号，客户", "kgstudio.asset.orders.steps.awaiting-payment": "等待付款", "kgstudio.asset.orders.steps.awaiting-shipment": "已付费 待发货", "kgstudio.asset.orders.steps.order-cancelled": "订单已取消", "kgstudio.asset.orders.steps.order-completed": "订单已完成", "kgstudio.asset.orders.steps.order-created": "订单已创建", "kgstudio.asset.orders.steps.paid": "已付费", "kgstudio.asset.orders.steps.sent": "已发送", "kgstudio.asset.orders.steps.shipping": "运输", "kgstudio.asset.order-status.awaiting-confirmation": "等待确认", "kgstudio.asset.order-status.awaiting-shipment": "等待发货", "kgstudio.asset.order-status.cancelled": "已取消", "kgstudio.asset.order-status.delivered": "已交付", "kgstudio.asset.order-status.shipping": "运输中", "kgstudio.asset.order-status.unpaid": "未付款", "kgstudio.asset.orders.title": "订单", "kgstudio.asset.payment-info.account-holder-name": "账户持有人姓名", "kgstudio.asset.payment-info.account-number": "账号", "kgstudio.asset.payment-info.bank-name": "银行名称", "kgstudio.asset.payment-info.bank-transfer": "银行转账", "kgstudio.asset.payment-info.branch-name": "分支名称", "kgstudio.asset.payment-info.currency": "付款货币", "kgstudio.asset.payment-info.payment-method": "付款方式", "kgstudio.asset.payment-info.section-title": "付款信息", "kgstudio.asset.payment-status.awaiting-refund": "等待退款", "kgstudio.asset.payment-status.paid": "已付费", "kgstudio.asset.payment-status.refunded": "已退款", "kgstudio.asset.payment-status.unpaid": "未付款", "kgstudio.asset.products.action": "行动", "kgstudio.asset.products.available": "可用", "kgstudio.asset.products.cancel-modal-cancel": "是的，取消", "kgstudio.asset.products.cancel-modal-hint": "你确定要取消吗？您刚才编辑的数据不会被保存", "kgstudio.asset.products.cancel-modal-stay": "不，留下来", "kgstudio.asset.products.edit-product": "编辑产品", "kgstudio.asset.products.fee": "费用", "kgstudio.asset.products.fee-free-reminder-description": "例如：消费 10,000 {quoteCurrency}将获得 (10000/{currentPrice}= {quoteAmount}{baseCurrency}({chain})", "kgstudio.asset.products.fee-free-reminder-title": "总价格 = 价格 × 代币金额", "kgstudio.asset.products.fee-included-reminder-description": "例如：消费 10,000 {quoteCurrency}将获得 （10000/{quoteDenominator}/{currentPrice})={quoteAmount}{baseCurrency}({chain})", "kgstudio.asset.products.fee-included-reminder-title": "总价格 =（1 + “手续费”）× 价格 × 代币數量", "kgstudio.asset.products.fee-included-reminder-with-min-fee-description": "例如：消费 10,000 {quoteCurrency}将获得 (10000-{minFee})/{currentPrice}= {quoteAmount}{baseCurrency}({chain})", "kgstudio.asset.products.fee-included-reminder-with-min-fee-title": "总价格 = 最低费用+ 价格 × 代币金额", "kgstudio.asset.products.handling-fee": "手续费", "kgstudio.asset.products.handling-fee-hint": "最小值：0%，最大值：100%", "kgstudio.asset.products.handling-fee-no": "无手续费", "kgstudio.asset.products.handling-fee-proportional": "手续费（已添加到价格中）", "kgstudio.asset.products.handling-fee-yes": "有手续费", "kgstudio.asset.products.image-size": "尺寸：200*200 像素", "kgstudio.asset.products.inventory-greater-than-zero": "显示库存应大于零。", "kgstudio.asset.products.inventory-less": "陈列品库存应少于 1,000,000,000。", "kgstudio.asset.products.limit-from-required": "订单限额为必填项", "kgstudio.asset.products.limit-to-required": "订单限额为必填项", "kgstudio.asset.products.limit-validation": "订单限额范围应为有效数字", "kgstudio.asset.products.minimum-fee": "最低费用", "kgstudio.asset.products.name-required": "商品名称为必填项", "kgstudio.asset.products.not-ready-for-publish-description": "请在发布之前完成产品设置的编辑", "kgstudio.asset.products.not-ready-for-publish-title": "产品信息尚未设置，因此无法发布。", "kgstudio.asset.products.order-limit": "订单限额", "kgstudio.asset.products.order-limits": "订单限额", "kgstudio.asset.products.price": "价格", "kgstudio.asset.products.price-required": "产品价格为必填项", "kgstudio.asset.products.product-name": "商品名", "kgstudio.asset.products.product-type": "产品类型", "kgstudio.asset.products.product-type-buy-crypto": "购买加密货币", "kgstudio.asset.products.publish": "发布", "kgstudio.asset.products.reset-all": "恢复", "kgstudio.asset.products.reset-all-title": "将信息恢复到前次设置", "kgstudio.asset.products.reset-image": "重置图像", "kgstudio.asset.products.reset-to-default": "重置为默认值", "kgstudio.asset.products.status-published": "已发表", "kgstudio.asset.products.status-unpublished": "未发表", "kgstudio.asset.products.stock": "库存", "kgstudio.asset.products.stock-hint": "資金池的可用库存：{tokenAmount}", "kgstudio.asset.products.trading-pair": "交易对", "kgstudio.asset.products.updated": "已更新", "kgstudio.asset.products.update-failure-toast": "更新 “{baseCurrency}/{quoteCurrency}({chain})” 失败。请再试一次。（错误代码:{code}）", "kgstudio.asset.products.update-success-toast": "“{baseCurrency}/{quoteCurrency}({chain})” 已成功更新。", "kgstudio.asset.recipient-address": "收款人（钱包地址）", "kgstudio.asset.recipient-send-by": "收款人（发送方{sendType}）", "kgstudio.asset.remain-limit-checking": "剩余的每日转账限额：正在检查...", "kgstudio.asset.remain-limit-info": "剩余的每日转账限额：{remainLimit}/{dailyTransferLimit}。", "kgstudio.asset.remain-limit-info-invalid": "您今天无法提交任何交易，也无法联系管理员调整您的交易限额。 ", "kgstudio.asset.remain-limit-invalid-hint": "调整金额或联系管理员调整您的转账限额。 ", "kgstudio.asset.threshold-info": "此交易需要批准（批准门槛：低于 ${transferApprovalThreshold}）。", "kgstudio.asset.token-and-gas-insufficient": "代币和主币余额不足。", "kgstudio.asset.token-insufficient": "代币余额不足。", "kgstudio.asset.transfer-amount": "转账金额", "kgstudio.asset.transfer-to": "转移到", "kgstudio.asset.transfer-validation": "在提交之前修复问题：", "kgstudio.asset.transfer-validation-amount-invalid": "转移代币金额无效。", "kgstudio.asset.transfer-validation-info-invalid": "交易说明和附件是批准的必要条件", "kgstudio.asset.transfer-validation-recipient-invalid": "收款人为必填项。在送出任何交易之前，单击 “风险扫描”。", "kgstudio.asset.tx-action-card.approve": "批准", "kgstudio.asset.tx-action-card.awaiting-approval.msg-approver": "请查看交易详情并做出决定：批准或拒绝交易。", "kgstudio.asset.tx-action-card.awaiting-approval.msg-normal": "此交易正在等待批准。如果你想加快流程，可以考虑与批准者取得联系。", "kgstudio.asset.tx-action-card.awaiting-release.msg-finance-manager": "请仔细考虑交易细节，继续发布交易以供执行或相应地拒绝交易。", "kgstudio.asset.tx-action-card.awaiting-release.msg-normal": "这笔交易正在排队等候释放。如果你想加快流程，可以考虑与财务经理取得联系。", "kgstudio.asset.tx-action-card.reject": "拒绝", "kgstudio.asset.tx-action-card.rejected.review-note.title": "评论笔记", "kgstudio.asset.tx-action-card.rejected.title": "此交易已被拒绝 ❌", "kgstudio.asset.tx-action-card.release": "发布", "kgstudio.asset.tx-action-card.release-check-list.balance-enough": "可用余额：足够 ({tokenBalance} {tokenSymbol})", "kgstudio.asset.tx-action-card.release-check-list.balance-loading": "检查组织的剩余余额", "kgstudio.asset.tx-action-card.release-check-list.balance-not-enough.desc": "在审批人批准此交易之前，请至少充值 {shortfallAmount}{tokenSymbol}（{chainName}）。", "kgstudio.asset.tx-action-card.release-check-list.balance-not-enough.title": "可用余额：不足 ({tokenBalance} {tokenSymbol})", "kgstudio.asset.tx-action-card.release-check-list.fee-enough": "预计转賬 gas fee：{fee}{tokenSymbol}", "kgstudio.asset.tx-action-card.release-check-list.fee-loading": "检查组织的剩余原生代币", "kgstudio.asset.tx-action-card.release-check-list.fee-not-enough.desc": "在审批人批准此交易之前，请至少充值 {shortfallAmount} {tokenSymbol}({chainName})。", "kgstudio.asset.tx-action-card.release-check-list.fee-not-enough.title": "可用的原生代币余额：不足。 ", "kgstudio.asset.tx-action-card.send-failed.msg": "欲了解更多详情，请点击下面的按钮重定向到区块链浏览器网站。", "kgstudio.asset.tx-action-card.send-failed.title": "交易失败 ❌", "kgstudio.asset.tx-action-card.sending.msg": "此交易已发布并正在发送。请注意，如果区块链拥塞，可能需要比预期更长的时间。\n\n欲了解更多详情，请点击下面的按钮重定向到区块链浏览器网站。", "kgstudio.asset.tx-action-card.send-success.msg": "欲了解更多详情，请点击下面的按钮重定向到区块链浏览器网站。", "kgstudio.asset.tx-action-card.send-success.title": "这笔交易已经完成 ✅", "kgstudio.asset.tx-action-card.title": "行动", "kgstudio.asset.tx-approval-modal.insufficient-balance-error.desc": "请在管理员存入足够的资金后重试 {shortfallAmount} {tokenSymbol} ({chainName})", "kgstudio.asset.tx-approval-modal.insufficient-balance-error.title": "错误：{tokenSymbol}({chainName}) 库存不足", "kgstudio.asset.tx-approval-modal.success.title": "成功获得批准", "kgstudio.asset.tx-confirm-approval-modal.approve": "确认批准", "kgstudio.asset.tx-confirm-approval-modal.insufficient-balance-error.description": "请在管理员存入足够的 ${amount} $ {tokenSymbol}(${chainName}) 后重试", "kgstudio.asset.tx-confirm-approval-modal.insufficient-balance-error.title": "错误：$ {tokenSymbol}(${chainName}) 库存不足", "kgstudio.asset.tx-confirm-approval-modal.send-token": "发送代币", "kgstudio.asset.tx-confirm-approval-modal.title": "确认批准", "kgstudio.asset.tx-confirm-approval-modal.warning.description": "请确保您已仔细审查交易细节并验证其合法性。", "kgstudio.asset.tx-confirm-approval-modal.warning.title": "您确定要将此交易标记为已批准吗？", "kgstudio.asset.tx-confirm-release-modal.release": "确认发布", "kgstudio.asset.tx-confirm-release-modal.warning.description": "请确保您已仔细审查交易细节并验证其合法性。", "kgstudio.asset.tx-confirm-release-modal.warning.title": "你确定要发布这笔交易吗？", "kgstudio.asset.tx-conform-release-modal.title": "确认发布", "kgstudio.asset.tx-detail.not-exist": "该组织中不存在此订单 ({orgName})。请改为查看相应的组织。", "kgstudio.asset.tx-detail.steps.awaiting-approval": "等待批准", "kgstudio.asset.tx-detail.steps.awaiting-release": "等待发布", "kgstudio.asset.tx-detail.steps.rejected": "已拒绝", "kgstudio.asset.tx-detail.steps.send-failed": "发送失败", "kgstudio.asset.tx-detail.steps.sending": "正在发送", "kgstudio.asset.tx-detail.steps.send-success": "成功发送", "kgstudio.asset.tx-detail.steps.submitted": "已提交", "kgstudio.asset.tx-detail.tx-info-card.tx-id": "交易编号", "kgstudio.asset.tx-error-generic": "发生了错误", "kgstudio.asset.tx-error-processing": "处理请求时出错。请稍后再试。", "kgstudio.asset.tx-failed": "交易发送失败。请再试一次。 ", "kgstudio.asset.tx-history.approved": "{name}已批准", "kgstudio.asset.tx-history.approved-by": "批准者", "kgstudio.asset.tx-history-card.approver": "批准者", "kgstudio.asset.tx-history-card.finance-manager": "财务经理", "kgstudio.asset.tx-history-card.submitted-by": "由... 提交", "kgstudio.asset.tx-history-card.title": "历史", "kgstudio.asset.tx-history-card.tx-hashes": "交易哈希", "kgstudio.asset.tx-history.latest-update": "最近更新", "kgstudio.asset.tx-history.rejected": "{name}已拒绝", "kgstudio.asset.tx-history.rejected-by": "拒绝者", "kgstudio.asset.tx-history.released": "{name}已放行", "kgstudio.asset.tx-history.released-by": "放行者", "kgstudio.asset.tx-history.submitted": "{name}已提交", "kgstudio.asset.tx-info-card.attachments": "附件", "kgstudio.asset.tx-info-card.blockchain": "区块链", "kgstudio.asset.tx-info-card.recipient": "收款人", "kgstudio.asset.tx-info-card.send-token": "发送代币", "kgstudio.asset.tx-info-card.title": "交易信息", "kgstudio.asset.tx-info-card.tx-note": "交易须知", "kgstudio.asset.tx-info-card.tx-status": "交易状态", "kgstudio.asset.tx-insufficient-balance": "資產餘額不足", "kgstudio.asset.tx-insufficient-balance-admin-recharge": "请管理员在您組織的資金池存入至少{insufficientAmount}{tokenName}({chainName})，然后重试", "kgstudio.asset.tx-limit-exceeded": "已超过转账限额", "kgstudio.asset.tx-limit-exceeded-contact-admin": "已超过转账上限，请联系管理员寻求帮助", "kgstudio.asset.tx-need-approval": "此交易仅在获得批准后发送。", "kgstudio.asset.tx-need-approval-hint": "請确保您的交易详情正确无误，因为提交后无法修改。", "kgstudio.asset.tx-rejection-modal.confirm-rejection": "确认拒绝", "kgstudio.asset.tx-rejection-modal.review-note.hint": "拒绝原因", "kgstudio.asset.tx-rejection-modal.review-note.required-error": "请提供拒绝的理由", "kgstudio.asset.tx-rejection-modal.review-note.title": "评论笔记", "kgstudio.asset.tx-rejection-modal.success.title": "已拒绝", "kgstudio.asset.tx-rejection-modal.title": "确认拒绝", "kgstudio.asset.tx-rejection-modal.warning.desc": "请确保您已仔细查看交易信息并提供拒绝理由。", "kgstudio.asset.tx-rejection-modal.warning.title": "你确定要拒绝这笔交易吗？", "kgstudio.asset.understand": "我明白", "kgstudio.asset.wallet-risk-check": "钱包风险检查", "kgstudio.audience.compliance": "合规", "kgstudio.audience.country": "国家", "kgstudio.audience.email": "电子邮件", "kgstudio.audience.kyc_status": "KYC", "kgstudio.audience.name": "姓名", "kgstudio.audience.nft_projects": "NFT 项目", "kgstudio.audience.phone": "电话", "kgstudio.audience.query-placeholder": "姓名、电子邮件或电话", "kgstudio.audience.wallet_id": "钱包 ID：", "kgstudio.auth.login.accept-crypto-for": "接受加密货币", "kgstudio.auth.login.continue-with-email": "以电子邮件继续", "kgstudio.auth.login.module.integration-options.description": "提供无代码解决方案、SDK 集成和便捷的 API 查询，以满足各种技术需求。", "kgstudio.auth.login.module.integration-options.title": "灵活集成（无代码、SDK、API）", "kgstudio.auth.login.module.low-fees.description": "最低费用，让创作者保留更多收入。", "kgstudio.auth.login.module.low-fees.title": "费用低", "kgstudio.auth.login.module.no-kyc.description": "无需进行 KYC 验证或商业登记。立即开始销售。", "kgstudio.auth.login.module.no-kyc.title": "没有限制", "kgstudio.auth.login.module.payment-options.description": "支持使用信用卡或直接钱包付款进行充值，以满足各种交易需求。", "kgstudio.auth.login.module.payment-options.title": "多种付款选项", "kgstudio.auth.login.rotator.artwork": "🎨 艺术作品", "kgstudio.auth.login.rotator.business": "💼 商业", "kgstudio.auth.login.rotator.community": "👥 社区", "kgstudio.auth.login.rotator.content": "📝 内容创作", "kgstudio.auth.login.rotator.digital-goods": "🎁 数码商品", "kgstudio.auth.login.rotator.website": "💻 网站", "kgstudio.check.change_acc_apologize": "对不起，你不能加入这个团队。", "kgstudio.check.change_acc_desc": "您当前的登录邮箱是{currentEmail}，请使用受邀请的邮箱代替：{inviteEmail}来登录。", "kgstudio.check.change_acc_link": "登出", "kgstudio.check.change_acc_title": "请更改您的登录账号以加入团队。", "kgstudio.check.change_acc_toast": "已注销当前账户。您将被重定向到新账户。", "kgstudio.check.invalid_desc": "请检查URL或联系您的组织管理员。", "kgstudio.check.invalid_link": "回到首页", "kgstudio.check.invalid_title": "对不起，此链接已过期或页面无法找到", "kgstudio.check.invitation-accepted-cta": "登录", "kgstudio.check.invitation-accepted-desc": "登录并开始您的旅程", "kgstudio.check.invitation-accepted-title": "恭喜。您已成功注册。", "kgstudio.check.loading_desc": "我们正在检查授权...", "kgstudio.check.loading_title": "请等待，不要离开或关闭此页面。", "kgstudio.common.accept": "接受", "kgstudio.common.account_setting": "账户设置", "kgstudio.common.add-fund": "添加基金", "kgstudio.common.address": "地址", "kgstudio.common.address-copied": "地址已复制到剪贴板", "kgstudio.common.all-tasks": "任务详情", "kgstudio.common.app-notification": "应用程序通知", "kgstudio.common.app-publish": "应用程序发布", "kgstudio.common.approved": "已批准", "kgstudio.common.asset": "AssetPro", "kgstudio.common.assets": "资产", "kgstudio.common.attachments": "附件", "kgstudio.common.back": "返回", "kgstudio.common.back-to-login": "返回登录", "kgstudio.common.billing": "账单", "kgstudio.common.blockchain": "区块链", "kgstudio.common.cancel": "取消", "kgstudio.common.case-management": "个案管理", "kgstudio.common.cdd-tasks": "CDD 任务", "kgstudio.common.change": "改变", "kgstudio.common.clear-filter": "清除條件", "kgstudio.common.close": "关闭", "kgstudio.common.community-links": "社区链接", "kgstudio.common.complete": "已完成", "kgstudio.common.compliance": "Compliance", "kgstudio.common.configuration": "钱包设置", "kgstudio.common.confirm-publish": "确认发布", "kgstudio.common.create": "创建", "kgstudio.common.create-a-task": "创建任务", "kgstudio.common.created": "创建于", "kgstudio.common.created_at": "创建于", "kgstudio.common.dapp-list": "DApp 列表", "kgstudio.common.data.analysis": "数据分析", "kgstudio.common.data.asset-pro": "AssetPro", "kgstudio.common.data.compliance": "Compliance", "kgstudio.common.data.nft-boost": "NFT 活动", "kgstudio.common.data.wallet": "钱包", "kgstudio.common.description": "描述", "kgstudio.common.discord": "Discord", "kgstudio.common.edit": "编辑", "kgstudio.common.editor": "编辑器", "kgstudio.common.engage": "行銷投放 (coming soon)", "kgstudio.common.error": "出了点问题。请稍后重试或联系我们的支持团队寻求帮助。", "kgstudio.common.expired": "已过期", "kgstudio.common.explorer-banner": "探险者横幅", "kgstudio.common.export-private-key": "导出私钥", "kgstudio.common.finance": "金融", "kgstudio.common.general": "一般", "kgstudio.common.get-started": "开始吧", "kgstudio.common.idv-tasks": "IDV 任务", "kgstudio.common.image": "图片", "kgstudio.common.in-app-message": "应用程序内消息", "kgstudio.common.insufficient_not_refunded": "不足（未退款）", "kgstudio.common.insufficient_refunded": "不足（已退款）", "kgstudio.common.invoice-pro": "发票 (PRO)", "kgstudio.common.kyc-form": "KYC 申請表", "kgstudio.common.kyt-tasks": "KYA 任务", "kgstudio.common.language.english": "English", "kgstudio.common.language.japanese": "日本語", "kgstudio.common.languages": "语言", "kgstudio.common.language.simplified-chinese": "中文（简体）", "kgstudio.common.language.spanish": "Español", "kgstudio.common.language.traditional-chinese": "中文（繁體）", "kgstudio.common.language.vietnamese": "Tiếng <PERSON>", "kgstudio.common.last-edited-time": "上次编辑时间", "kgstudio.common.line-id": "LINE ID", "kgstudio.common.liquidity": "流动性", "kgstudio.common.logout": "登出", "kgstudio.common.marketing-tools": "营销工具", "kgstudio.common.market-settings": "商店设置", "kgstudio.common.maxInputHint": "最多进入 1000 字", "kgstudio.common.member-id": "员工 ID", "kgstudio.common.members": "成员", "kgstudio.common.minimum": "最低", "kgstudio.common.my-role": "我的角色", "kgstudio.common.my-shop": "我的商店", "kgstudio.common.next": "下一步", "kgstudio.common.next-step": "下一步", "kgstudio.common.nft-airdrop": "NFT 空投", "kgstudio.common.nft-boost": "NFT 發行工具", "kgstudio.common.note-and-attachments": "备注和附件", "kgstudio.common.notification": "通知", "kgstudio.common.off-ramp": "", "kgstudio.common.operators": "运营商", "kgstudio.common.optional": "非必填", "kgstudio.common.orders": "订单中心", "kgstudio.common.organization-id-required": "组织 ID 为必填项", "kgstudio.common.overview": "概览", "kgstudio.common.page-desc": "显示每页行数", "kgstudio.common.payment-address": "付款地址", "kgstudio.common.pending": "待处理", "kgstudio.common.prev-step": "前一步", "kgstudio.common.processing": "加载中...", "kgstudio.common.products": "产品", "kgstudio.common.profile": "概况", "kgstudio.common.project": "项目", "kgstudio.common.project-updated": "项目信息已更新！", "kgstudio.common.push-notification": "推送通知", "kgstudio.common.recaptcha-error": "无法验证谷歌 reCAPTCHA。请刷新网页或联系我们的支持团队寻求帮助。", "kgstudio.common.recipient": "收貨人", "kgstudio.common.reject": "拒绝", "kgstudio.common.rejected": "已拒绝", "kgstudio.common.reset": "重置", "kgstudio.common.revenue": "收入", "kgstudio.common.revert": "拒绝并取消", "kgstudio.common.review": "審查中心", "kgstudio.common.roles": "角色", "kgstudio.common.save": "保存", "kgstudio.common.save-changes": "已保存更改！", "kgstudio.common.save-draft": "保存草稿", "kgstudio.common.search": "搜寻", "kgstudio.common.send": "发送", "kgstudio.common.send-now": "发送", "kgstudio.common.send-to": "发送至", "kgstudio.common.send-token": "发送代币", "kgstudio.common.settings": "设置", "kgstudio.common.start": "开始", "kgstudio.common.submit": "提交", "kgstudio.common.submit-request": "提交", "kgstudio.common.success": "成功", "kgstudio.common.successfully-copied": "成功复制！", "kgstudio.common.supported-chains": "支持的区块链", "kgstudio.common.system-settings": "系统设置", "kgstudio.common.telegram": "Telegram", "kgstudio.common.top-up": "充值", "kgstudio.common.total": "总计", "kgstudio.common.transaction-status": "交易状态", "kgstudio.common.transfer": "转移", "kgstudio.common.treasury": "資金池", "kgstudio.common.twitter": "Twitter", "kgstudio.common.tx-hash": "交易哈希", "kgstudio.common.type": "类型", "kgstudio.common.type-some-description": "请在此处键入一些描述。", "kgstudio.common.unset": "取消设置", "kgstudio.common.update": "更新", "kgstudio.common.updated_at": "更新于", "kgstudio.common.update-time": "更新时间", "kgstudio.common.uploading": "正在上传...", "kgstudio.common.user": "用户设置", "kgstudio.common.user360": "用户 360", "kgstudio.common.user-management": "用户管理", "kgstudio.common.user-not-found": "找不到工作室用户。请联系您的组织管理员。", "kgstudio.common.users": "用户", "kgstudio.common.wallet": "钱包生成器", "kgstudio.compliance.cdd-tasks": "CDD 任务", "kgstudio.compliance.idv-tasks": "IDV 任务", "kgstudio.compliance.kyt-tasks": "KYA 任务", "kgstudio.compliance.title": "Compliance", "kgstudio.data.actions-section": "行动", "kgstudio.data.active-users": "活跃用户", "kgstudio.data.ai-generate": "人工智能生成！", "kgstudio.data.ai-recommend": "人工智能推荐", "kgstudio.data.all": "全部", "kgstudio.data.app-store-info-language": "应用商店信息语言", "kgstudio.data.asset-pro.date.all": "全部", "kgstudio.data.asset-pro.date.last-14-days": "最近 14 天", "kgstudio.data.asset-pro.date.last-30-days": "最近 30 天", "kgstudio.data.asset-pro.date.last-7-days": "最近 7 天", "kgstudio.data.asset-pro.date.title": "日期", "kgstudio.data.asset-pro.error": "无法生成数据，请稍后重试。", "kgstudio.data.asset-pro.loading": "正在为 AssetPro 数据生成仪表板...", "kgstudio.data.asset-pro.retry": "重试", "kgstudio.data.balance": "資產餘額", "kgstudio.data.balance-greater-than-zero": "余额 > 0", "kgstudio.data.budget-title": "预算", "kgstudio.data.choose-plan": "选择套餐", "kgstudio.data.churn-rate": "流失率", "kgstudio.data.churn-users": "流失用户", "kgstudio.data.compliance.cdd-tasks": "案例审查数量", "kgstudio.data.compliance.form-submission": "表单提交", "kgstudio.data.compliance.idv-tasks": "身份验证案件数", "kgstudio.data.compliance.kyc-status.title": "KYC 验证", "kgstudio.data.compliance.no-data": "尚无数据可供分析", "kgstudio.data.compliance.pending-review": "待审核客戶", "kgstudio.data.compliance.personal-info.title": "个人信息", "kgstudio.data.compliance.verified-customers": "已验证客户", "kgstudio.data.compliance.v-wallet-usage.title": "已验证客戶之钱包啟用", "kgstudio.data.compliance.v-wallet-usage.tooltip": "经过KYC验证的用户是否激活（下载并登录）了他们的Kryptogo钱包。", "kgstudio.data.create_nft_collection": "创建 NFT 收藏夹", "kgstudio.data.discard": "丢弃", "kgstudio.data.engage.compliance": "合规", "kgstudio.data.engage.country": "国家", "kgstudio.data.engage-create": "创建参与度", "kgstudio.data.engage.email": "电子邮件", "kgstudio.data.engage.kyc": "KYC", "kgstudio.data.engage-list": "参与项目清单", "kgstudio.data.engage.name": "姓名", "kgstudio.data.engage.nft-projects": "NFT 项目", "kgstudio.data.engage.phone": "电话", "kgstudio.data.engage-title": "参与", "kgstudio.data.engage.wallet-id": "钱包 ID", "kgstudio.data.estimated-gas-fee": "预计汽油费", "kgstudio.data.estimated-reach": "预计覆盖范围", "kgstudio.data.events": "活动", "kgstudio.data.evm-wallet": "EVM 钱包", "kgstudio.data.file_types_supported": "支持的图片：JPG、PNG、GIF、SVG。最大大小：10 MB（重命名文件格式不起作用。如果您不支持的图像格式，请使用文件转换服务。）", "kgstudio.data.header": "钱包数据", "kgstudio.data.increase-user-retention": "提高用户留存率", "kgstudio.data.increase-wallet-user-activity": "注册用户激活", "kgstudio.data.increase-wallet-user-retention": "电子钱包用户留存率", "kgstudio.data.invalid": "请确认输入的信息是否正确", "kgstudio.data.login": "登录", "kgstudio.data.new-users": "新用户", "kgstudio.data.nft_opensea_banner_title": "OpenSea 橫幅大圖", "kgstudio.data.notification.1-button": "1 个按钮", "kgstudio.data.notification.2-buttons": "2 个按钮", "kgstudio.data.notification.body": "身体", "kgstudio.data.notification.buttons": "按钮", "kgstudio.data.notification.enter-title": "输入标题", "kgstudio.data.notification.file-types-supported": "支持的图片：JPG、PNG、GIF、SVG。最大大小：10 MB（重命名文件格式不起作用。如果您不支持的图像格式，请使用文件转换服务。）", "kgstudio.data.notification.first-button": "第一个按钮", "kgstudio.data.notification.image": "图片", "kgstudio.data.notification.no-button": "没有按钮", "kgstudio.data.notification.recommended-size": "建议尺寸：1400 x 350 px", "kgstudio.data.notification.text-placeholder": "文本", "kgstudio.data.recommended_size": "建议尺寸：600 x 200 px", "kgstudio.data.recommended-target": "推荐目标", "kgstudio.data.registered-in-7D": "7 天内注册", "kgstudio.data.registered-users": "注册用户", "kgstudio.data.retention": "留存", "kgstudio.data.retention-rate": "留存率", "kgstudio.data.select_from_nft_boost": "从 NFT Boost 中选择", "kgstudio.data.select-multiple-actions": "您可以为用户设置多种类型的资产/信息。", "kgstudio.data.send-currency": "发送货币", "kgstudio.data.send-nft": "发送 NFT", "kgstudio.data.send-notification": "发送通知", "kgstudio.data.shop-info-language": "商店信息语言版本", "kgstudio.data.since-last-month": "自上个月以来", "kgstudio.data.specify-engage-time": "排程", "kgstudio.data.target-placeholder": "您的目标，例如提高留存率", "kgstudio.data.target-section": "目标", "kgstudio.data.target-title": "目标", "kgstudio.data.time-option-custom": "自定义", "kgstudio.data.time-option-immediately": "立即", "kgstudio.data.time-section": "时间", "kgstudio.data.top-users": "热门用户", "kgstudio.data.total-balance": "总余额", "kgstudio.data.tron-wallet": "Tron 钱包", "kgstudio.data.try-more": "多试一试", "kgstudio.data.tx-events": "交易事件", "kgstudio.data.upload-branding-assets": "上传品牌资产", "kgstudio.data.upload-csv-list": "上传.csv 列表", "kgstudio.data.use-assetpro": "使用 AssetPro 向您的目标用户发送加密货币", "kgstudio.data.use-nft-boost": "从 NFT Boost 现有的 NFT 物品中进行选择", "kgstudio.data.user": "用户", "kgstudio.data.user-activities": "用户活动", "kgstudio.data.users-balance-greater": "余额 > 0", "kgstudio.data.use-wallet-notification": "向您的钱包用户发送通知和公告", "kgstudio.data.wallet": "钱包", "kgstudio.data.wallet-address": "钱包地址", "kgstudio.data.wallet-balance-greater": "钱包余额 > 0", "kgstudio.data.wallets": "钱包", "kgstudio.data.your-prompt": "你的提示", "kgstudio.dna.create-at": "创建于", "kgstudio.dna.token-holdings": "代币持有量", "kgstudio.engagement.actions-title": "行动", "kgstudio.engagement.activity": "活动", "kgstudio.engagement.all": "全部", "kgstudio.engagement.asset-balance": "资产余额", "kgstudio.engagement.behavior": "行为", "kgstudio.engagement.engaged": "行銷投放", "kgstudio.engagement.methods": "参与方法", "kgstudio.engagement.name": "项目名称", "kgstudio.engagement.nft-claim-rate": "NFT 申领率", "kgstudio.engagement.notification-visit": "通知访问", "kgstudio.engagement.rewards-redeem-rate": "奖励兑换率", "kgstudio.engagement.send-currency": "发送货币", "kgstudio.engagement.send-nft": "发送 NFT", "kgstudio.engagement.send-notification": "发送通知", "kgstudio.engagement.target-settings": "目标设置", "kgstudio.engagement.time": "时间", "kgstudio.engagement.title": "参与度", "kgstudio.engagement.users": "用户", "kgstudio.error.cant-find-customer": "抱歉，该用户尚未注册您的服务。如果您想向该用户转账，需请他们先注册并登录您的KYC表单或钱包。", "kgstudio.error.cant-find-user": "对不起，我们找不到此用户。", "kgstudio.error.general-error": "发生错误。代码： ", "kgstudio.error.insufficient-balance": "余额不足。请在贵组织的资金池中添加更多资金。", "kgstudio.error.no-access": "你没有访问权限。请使用经过验证的钱包地址登录。", "kgstudio.error.no-organization": "抱歉，你还没有加入任何组织", "kgstudio.error.no-organization-contact": "请联系您的组织管理员，或联系KryptoGo开始您的商业计划。", "kgstudio.error.out-of-range": "输入值超出范围。", "kgstudio.error.permission-denied.desc": "您没有权限访问此页面。", "kgstudio.error.permission-denied.title": "权限被拒绝。", "kgstudio.error.please-try-again": "请稍后再试", "kgstudio.error.resource-not-found.desc": "你要找的页面不存在。", "kgstudio.error.resource-not-found.title": "未找到资源。", "kgstudio.error.something-went-wrong": "抱歉，出了点问题", "kgstudio.error.try-again": "出现了一些错误，请稍后再试。", "kgstudio.error.upload-attachment": "上传附件失败", "kgstudio.error.upload-image-failed": "上传图片失败", "kgstudio.error.upload-max-files": "已超过最大文件数。", "kgstudio.error.upload-max-file-size": "文件大小超过 10 MB。", "kgstudio.error.upload-unsupported-file": "不支持的文件格式。", "kgstudio.error.user-not-found": "未找到用户。", "kgstudio.home.awaiting-approval-tx.title": "待批准交易", "kgstudio.home.awaiting-release-tx.title": "待发布交易", "kgstudio.home.duplicate-org-name": "组织名称已经存在", "kgstudio.home.edit-organization.icon-hint1": "支持的格式：JPG、PNG、WEBP、SVG。", "kgstudio.home.edit-organization.icon-hint2": "推荐尺寸：100 x 100 px", "kgstudio.home.edit-organization.icon-hint3": "最大 10 MB", "kgstudio.home.edit-organization.icon-title": "图标", "kgstudio.home.edit-organization.name-error1": "名称必须为 2-20 个字符", "kgstudio.home.edit-organization.name-error2": "名称包含无效字符（@、#、$、/、*、...）", "kgstudio.home.edit-organization.name-placeholder": "组织", "kgstudio.home.edit-organization.name-title": "姓名", "kgstudio.home.edit-organization.success": "组织设置已更新！", "kgstudio.home.edit-organization-title": "编辑组织", "kgstudio.home.joyride.step1": "在账户设置页面中，您可以使用 AI 代理创建加密支付页面或将支付功能集成到您的现有应用程序中，适合开发人员。", "kgstudio.home.joyride.step2": "在产品列表页面中，您可以创建自己的产品并获得单页付款链接以与任何人共享。", "kgstudio.home.joyride.step3": "在国库页面，您可以查看钱包的当前收入并管理您的资金池。", "kgstudio.home.joyride.step4": "您也可以点击此处的按钮访问上述页面。", "kgstudio.home.joyride.step5": "还没有产品吗？点击此处创建您的第一个产品。", "kgstudio.home.kyc-pending.title": "KYC 待审核", "kgstudio.home.kyc-pending.tooltip": "有待审查的KYC申请的数量", "kgstudio.home.my-pending-tx.title": "我的待处理交易", "kgstudio.home.orders-pending.title": "待处理订单", "kgstudio.home.orders-pending.tooltip": "尚未处理的订单数量", "kgstudio.home.payment.api-key-settings": "API 密钥设置", "kgstudio.home.payment.create-product": "创建产品", "kgstudio.home.payment.data": "付款资讯", "kgstudio.home.payment.manage-wallet": "管理钱包", "kgstudio.home.payment.total-order": "订单总数", "kgstudio.home.payment.total-revenue": "总收入", "kgstudio.home.payment.unique-customer": "独立客户", "kgstudio.image.upload-required": "请上传图片", "kgstudio.kyc-status.pending": "待审核", "kgstudio.kyc-status.rejected": "拒绝", "kgstudio.kyc-status.transfer-hint.pending": "请注意，此用户的KYC审核仍在进行中。请尽快完成审核并谨慎交易。", "kgstudio.kyc-status.transfer-hint.rejected": "请注意，此用户未通过KYC审核，请谨慎交易。", "kgstudio.kyc-status.transfer-hint.unverified": "此用户尚未通过KYC验证，请谨慎交易。", "kgstudio.kyc-status.transfer-hint.verified": "此用户已通过 KYC 验证！", "kgstudio.kyc-status.unverified": "未验证", "kgstudio.kyc-status.verified": "已验证", "kgstudio.login.with-google": "", "kgstudio.message.input-message-content": "请输入信息内容", "kgstudio.nft.airdrop": "空投", "kgstudio.nft.back-to-list": "返回项目清单", "kgstudio.nft.balance-and-fee": "您的 Polygon 钱包余额为 {balance} POL，發布此專案约需 {createCollectionFee} POL 。", "kgstudio.nft.campaign": "活动", "kgstudio.nft.claimed": "已領取", "kgstudio.nft.claimed-total": "已申领/总计", "kgstudio.nft.collection-name-hint": "请输入 40 个字符以内的字母数字字符。", "kgstudio.nft.collection-symbol-hint": "建议在 10 个字母以内，通常是馆藏名称的缩写", "kgstudio.nft.confirm-publish": "确认发布", "kgstudio.nft.create-collection-fee": "创建此 NFT 收藏專案大约需要 {createCollectionFee}POL。", "kgstudio.nft.delivery-method-title": "配送方式", "kgstudio.nft.edit-button-text": "编辑", "kgstudio.nft.end-date-title": "结束日期", "kgstudio.nft.error.collection-name-existed": "NFT 名称已经存在", "kgstudio.nft.error.project-not-found": "未找到项目", "kgstudio.nft.favicon-title": "网站图标", "kgstudio.nft.form.banner-file-types": "支持的图片：JPG、PNG、GIF、SVG。最大大小：100 MB。（重命名文件格式不起作用。如果您不支持的图像格式，请使用文件转换服务。）", "kgstudio.nft.form.banner-recommended-size": "建议尺寸：1400 x 350 px。", "kgstudio.nft.form.collection-description": "NFT 描述", "kgstudio.nft.form.collection-description-hint": "最多 1000 个字符。", "kgstudio.nft.form.collection-name": "NFT 藏品名称", "kgstudio.nft.form.collection-name-hint": "请输入最多 40 个字符的字母数字文本。", "kgstudio.nft.form.contract-schema-name": "合约架构名称", "kgstudio.nft.form.favicon": "网站图标图片", "kgstudio.nft.form.favicon-image-title": "网站图标图片", "kgstudio.nft.form.max-supply": "供应量", "kgstudio.nft.form.max-supply-hint": "发行 NFT 約需要礦工费：{mintFee} POL（根据当前的网络活动而有所不同）。", "kgstudio.nft.form.max-supply-label": "NFT 供应量", "kgstudio.nft.form.mint-time-customized": "自定义时间", "kgstudio.nft.form.mint-time-end": "收货结束", "kgstudio.nft.form.mint-time-end-hint": "无法将时间设置为晚于 2038/01/01 00:00（世界标准时间）。", "kgstudio.nft.form.mint-time-end-instant": "無 (將於 2038/01/01 結束)", "kgstudio.nft.form.mint-time-instant": "立即（NFT 可以在发布后立即被領取）", "kgstudio.nft.form.mint-time-start": "开始接收", "kgstudio.nft.form.nft-image": "NFT 图片", "kgstudio.nft.form.nft-opensea-banner": "NFT OpenSea Banner", "kgstudio.nft.form.placeholder.description": "提供您的 NFT 的详细描述。", "kgstudio.nft.form.received-method": "接收方法", "kgstudio.nft.form.received-method-address": "钱包地址", "kgstudio.nft.form.received-method-email": "输入电子邮件", "kgstudio.nft.form.received-method-phone": "输入电话号码", "kgstudio.nft.form.subtitle": "副标题", "kgstudio.nft.form.symbol-name": "NFT 代号", "kgstudio.nft.form.symbol-name-hint": "最多：10 个字符。仅限英文字母。不能包含空格。通常基于短名称，例如 KGYC", "kgstudio.nft.form.title": "页面标题", "kgstudio.nft.form.upload-ico-desc": "请上传.ico 文件", "kgstudio.nft.form.upload-icon-file": "请上传.ico 文件", "kgstudio.nft.free-claim": "免费领取 NFT", "kgstudio.nft.go-to-project": "查看详情", "kgstudio.nft.image-collection-item": "NFT 收藏图片、NFT 物品图片", "kgstudio.nft.image-file-types": "支持的格式：JPG、PNG、GIF、SVG。最大大小：10 MB（重命名文件格式不起作用。如果您不支持的图像格式，请使用文件转换服务。）", "kgstudio.nft.image-recommended-size": "推荐尺寸：2000 x 2000 px", "kgstudio.nft.info.contract": "合同地址", "kgstudio.nft.info.creator": "创作者", "kgstudio.nft.info.na": "N/A", "kgstudio.nft.insufficient-balance": "余额不足！", "kgstudio.nft.label.success-sms-preview": "成功短信预览", "kgstudio.nft.mint-fee": "使用者每領取一個 NFT 時，礦工費約需 {mintFee} POL（根据当前的网络活动而有所不同）。", "kgstudio.nft.mint-page.na": "尚无活动链接。", "kgstudio.nft.mint-page-name": "NFT 活动网站", "kgstudio.nft.mint-page.pending": "正在生成您的活动网站... 活动链接将很快可用！", "kgstudio.nft.mint-page-title": "NFT 铸币页面", "kgstudio.nft.mint-time": "铸币时间", "kgstudio.nft.modal.edit-mint-page": "编辑 NFT 铸币页面", "kgstudio.nft.next-step": "下一步", "kgstudio.nft.nft-collection": "NFT 系列", "kgstudio.nft.nft-collection-chain": "NFT 将在 Polygon 链上发行。", "kgstudio.nft.nft-projects": "NFT 项目", "kgstudio.nft.notification.draft": "草稿状态：您的 NFT 尚未发布！", "kgstudio.nft.notification.failed": "发布你的 NFT 时出错！", "kgstudio.nft.notification.pending": "NFT 合约部署正在进行中。请稍后再回来查看此页面。", "kgstudio.nft.notification.published": "您的 NFT 已成功发布！", "kgstudio.nft.overview": "概述", "kgstudio.nft.placeholder.success-sms": "提供您的 NFT 的详细描述。", "kgstudio.nft.preview.collection": "NFT 系列预览", "kgstudio.nft.preview.mint-page": "预览 - NFT 活动网站", "kgstudio.nft.prev-step": "上一步", "kgstudio.nft.processing": "处理中，请稍候", "kgstudio.nft.processing-description": "NFT 合约正在部署中，可能需要几分钟。在等待 NFT 发布的同时，您可以前往 NFT 项目页面获取更新或返回 NFT 项目列表继续浏览。", "kgstudio.nft.qr-code-title": "二维码", "kgstudio.nft.recharge-balance": "您需要向{balanceDifference}钱包充值 POL 才能发布 NFT 项目。", "kgstudio.nft.reward": "奖励", "kgstudio.nft.save-draft": "保存草稿", "kgstudio.nft.saved-successfully-toast": "NFT 项目 “{collectionName}” 的草稿已保存！", "kgstudio.nft.scan-to-visit": "扫一扫访问", "kgstudio.nft.start-date-title": "开始日期", "kgstudio.nft.status.claimed": "已申领", "kgstudio.nft.status.claim-rate": "申领率", "kgstudio.nft.status.draft": "草稿", "kgstudio.nft.status.failed": "失败了", "kgstudio.nft.status.pending": "待定", "kgstudio.nft.status.published": "已发布", "kgstudio.nft.status.total": "总计", "kgstudio.nft.step.check": "预览并确认", "kgstudio.nft.step.collection": "填写 NFT 收藏品信息", "kgstudio.nft.step.mint": "配置 NFT 分发", "kgstudio.nft.subtitle-title": "副标题", "kgstudio.nft.success-sms-title": "成功短信通知", "kgstudio.nft.text.success-sms": "【NFT 接收通知】~ 恭喜收到 {collectionName}  NFT！请打开 KryptoGO 钱包应用程序并使用您的电话号码登录.（应用程序下载链接：{appLink}）", "kgstudio.nft.title-title": "标题", "kgstudio.nft.total": "总计", "kgstudio.nft.validation.collection-desc": "请输入 NFT 系列描述", "kgstudio.nft.validation.collection-name": "请输入 NFT 收藏名称", "kgstudio.nft.validation.collection-name-max": "NFT 收藏名称不能超过 40 个字符", "kgstudio.nft.validation.collection-name-min": "请输入 NFT 收藏名称", "kgstudio.nft.validation.enter-collection-abbreviation": "请输入 NFT 收藏缩写", "kgstudio.nft.validation.enter-total-supply": "请输入 NFT 总供应量", "kgstudio.nft.validation.only-english-and-whitespace": "只允许使用英文字符和空格", "kgstudio.nft.wallet-balance": "你的 Polygon 钱包余额為", "kgstudio.nft.wallet-balance-matic": "POL。请确保您的余额足以支付创建和发行 NFT 的礦工费。", "kgstudio.onboarding.category-arts": "", "kgstudio.onboarding.category-arts-advice": "", "kgstudio.onboarding.category-arts-description": "", "kgstudio.onboarding.category-arts-name": "", "kgstudio.onboarding.category-educational": "", "kgstudio.onboarding.category-educational-advice": "", "kgstudio.onboarding.category-educational-description": "", "kgstudio.onboarding.category-educational-name": "", "kgstudio.onboarding.category-other": "", "kgstudio.onboarding.category-products": "", "kgstudio.onboarding.category-products-advice": "", "kgstudio.onboarding.category-products-description": "", "kgstudio.onboarding.category-products-name": "", "kgstudio.onboarding.check-store": "", "kgstudio.onboarding.close": "", "kgstudio.onboarding.copy-link": "", "kgstudio.onboarding.create-error": "", "kgstudio.onboarding.currency": "", "kgstudio.onboarding.fill-required-fields": "", "kgstudio.onboarding.go": "", "kgstudio.onboarding.image": "", "kgstudio.onboarding.next": "", "kgstudio.onboarding.prev": "", "kgstudio.onboarding.product-description": "", "kgstudio.onboarding.product-name": "", "kgstudio.onboarding.product-price": "", "kgstudio.onboarding.receive-address": "", "kgstudio.onboarding.revenue-over-3000": "", "kgstudio.onboarding.revenue-under-3000": "", "kgstudio.onboarding.skip": "", "kgstudio.onboarding.step1.title": "", "kgstudio.onboarding.step2.title": "", "kgstudio.onboarding.step3.title": "", "kgstudio.onboarding.step4.title": "", "kgstudio.onboarding.step6.title": "", "kgstudio.onboarding.step7.title": "", "kgstudio.onboarding.step8.description": "", "kgstudio.onboarding.step8.title": "", "kgstudio.onboarding.supported-address": "", "kgstudio.onboarding.supported-chains": "", "kgstudio.operators.approval-threshold-desc": "转账达到此指定金额后需要获得批准。", "kgstudio.operators.daily-transfer-limit": "每日转账限额", "kgstudio.operators.edit-operator": "编辑操作员", "kgstudio.operators.page-title": "运营設定", "kgstudio.operators.placeholder": "搜索姓名、电子邮件、ID", "kgstudio.operators.threshold-amount": "阈值金额", "kgstudio.operators.title": "AssetPro 运营人員", "kgstudio.operators.transfer-approval-threshold": "转帳需批准门槛", "kgstudio.operators.transfer-approval-threshold-desc": "超过此阈值的交易需要获得批准才能发送。", "kgstudio.operators.transfer-limit-desc": "运营商可以在 AssetPro 中转账的最大每日金额，每天 00:00 重置。", "kgstudio.operators.transfer-limit-error": "阈值金额不能超过每日转账限额", "kgstudio.organization.create.back": "返回", "kgstudio.organization.create.back-to-login": "返回登录", "kgstudio.organization.create.button": "创建组织", "kgstudio.organization.create.email-description": "此电子邮件将与您在组织中的帐户关联。", "kgstudio.organization.create.email-placeholder": "输入你的电子邮件地址", "kgstudio.organization.create.error.failed": "无法创建组织。请再试一次。", "kgstudio.organization.create.error.login-failed": "创建组织后登录失败。请重试。", "kgstudio.organization.create.error.missing-token": "身份验证令牌丢失。请尝试再次登录。", "kgstudio.organization.create.login-success": "成功登录！", "kgstudio.organization.create.org-name-placeholder": "输入组织名称", "kgstudio.organization.create.subtitle.existing-user": "创建新组织", "kgstudio.organization.create.subtitle.new-user": "创建您的组织以开始使用", "kgstudio.organization.create.success": "组织已成功创建", "kgstudio.overview.applications": "应用程序", "kgstudio.overview.assetpro-intro": "轻量级、安全的加密现金流管理系统。", "kgstudio.overview.compliance-intro": "全球合规，适应监管变化。", "kgstudio.overview.create-date": "创建日期", "kgstudio.overview.nft-intro": "无需代码，即可轻松创建 NFT 活动。", "kgstudio.overview.no-access": "看来你没有这个模块的权限。如果你想使用它，请联系你的系统所有者来帮助你激活它。", "kgstudio.overview.user360-intro": "您的 Web3 命令中心。", "kgstudio.overview.wallet-intro": "以用户为中心的品牌专属钱包。", "kgstudio.page.input-page-subtitle": "请输入页面副标题", "kgstudio.page.input-page-title": "请输入页面标题", "kgstudio.page.page-size-description": "每页显示行数：{pageSize}", "kgstudio.payment.accent-color": "主颜色", "kgstudio.payment.add-field": "添加字段", "kgstudio.payment.aggregated-amount": "汇总金额", "kgstudio.payment.all-clients": "所有用户", "kgstudio.payment.amount": "金额", "kgstudio.payment.button-preview": "按钮预览", "kgstudio.payment.callback-dashboard": "Callback 控制面板", "kgstudio.payment.callback-details": "回拨详情", "kgstudio.payment.callback-id": "回调 ID", "kgstudio.payment.callback-payload": "回调负载", "kgstudio.payment.callback-result": "Callback 结果", "kgstudio.payment.callback-status": "Callback 状态", "kgstudio.payment.callback-type": "Callback 类型", "kgstudio.payment.callback-url": "回调网址", "kgstudio.payment.callback-url-placeholder": "我们会将付款结果发送到此 URL", "kgstudio.payment.chain-id": "", "kgstudio.payment.chain-id-desc": "", "kgstudio.payment.client-id": "客户端 ID", "kgstudio.payment.column-setting": "专栏", "kgstudio.payment.copy-button": "复制付款按钮", "kgstudio.payment.copy-link": "复制付款链接", "kgstudio.payment.create-item-error": "无法创建付款项目", "kgstudio.payment.create-item-success": "付款项目已成功创建", "kgstudio.payment.create-payment": "创建付款", "kgstudio.payment.create-product": "创建产品", "kgstudio.payment.create-product-title": "创建产品", "kgstudio.payment.create-payment-intent": "创建付款意图", "kgstudio.payment.create-intent-success": "付款意图创建成功", "kgstudio.payment.create-intent-error": "创建付款意图失败", "kgstudio.payment.payment-intent-details": "付款意图详情", "kgstudio.payment.configure-payment-intent-details": "配置付款意图详情", "kgstudio.payment.crypto-amount": "加密金额", "kgstudio.payment.crypto-price": "加密价格", "kgstudio.payment.currency": "货币", "kgstudio.payment.custom-fields": "自定义字段", "kgstudio.payment.date-range": "日期范围", "kgstudio.payment.deadline": "最后期限", "kgstudio.payment.delete-item-confirmation": "您确定要删除此付款项目吗？", "kgstudio.payment.delete-item-title": "删除付款项目", "kgstudio.payment.duration": "持续时间", "kgstudio.payment.edit-product-title": "编辑产品", "kgstudio.payment.error-loading-callbacks": "加载 Callback 日志失败", "kgstudio.payment.error-loading-oauth-clients": "加载 OAuth 客户端时出错", "kgstudio.payment.error-url": "错误网址", "kgstudio.payment.error-url-placeholder": "输入错误重定向 URL", "kgstudio.payment.event-details": "活动详情", "kgstudio.payment.event-type": "事件类型", "kgstudio.payment.export-csv": "导出 CSV", "kgstudio.payment.failed": "失败", "kgstudio.payment.fiat-amount": "法定金额", "kgstudio.payment.fiat-currency": "法定货币", "kgstudio.payment.field-key": "钥匙", "kgstudio.payment.field-label": "字段标签", "kgstudio.payment.field-label-required": "字段标签为必填项", "kgstudio.payment.field-name": "字段名", "kgstudio.payment.field-name-duplicate": "", "kgstudio.payment.field-name-required": "字段名称为必填项", "kgstudio.payment.field-type": "字段类型", "kgstudio.payment.field-value": "价值", "kgstudio.payment.group-key": "群组密钥", "kgstudio.payment.group-key-search-placeholder": "按群组密钥搜索", "kgstudio.payment.http-code": "HTTP 代码", "kgstudio.payment.intent-id": "意图 ID", "kgstudio.payment.kg-deep-link": "KG 深度链接", "kgstudio.payment.merchant-email": "商家邮箱", "kgstudio.payment.merchant-settings": "商家设置", "kgstudio.payment.merchant-settings-desc": "卖家电子邮件和主颜色显示在付款页面上。客户可以通过电子邮件联系商家寻求支持，重点色突出显示了关键元素。", "kgstudio.payment.new-field": "新领域", "kgstudio.payment.open-page": "打开付款页面", "kgstudio.payment.optional-field": "可选字段", "kgstudio.payment.optional-fields": "可选字段", "kgstudio.payment.order-data": "订单数据", "kgstudio.payment.order-data-expanded": "扩展订单数据", "kgstudio.payment.order-data-fields": "订单数据字段", "kgstudio.payment.organization-icon": "组织图标", "kgstudio.payment.organization-icon-placeholder": "输入组织图标 URL", "kgstudio.payment.organization-id": "组织 ID", "kgstudio.payment.organization-id-placeholder": "输入组织 ID", "kgstudio.payment.organization-name": "组织名称", "kgstudio.payment.organization-name-placeholder": "输入组织名称", "kgstudio.payment.organization-required": "组织为必填项", "kgstudio.payment.recipient-details": "收款人详情", "kgstudio.payment.recipient-name": "收款人姓名", "kgstudio.payment.recipient-name-placeholder": "组织名称", "kgstudio.payment.contact-email": "联系邮箱", "kgstudio.payment.contact-email-placeholder": "<EMAIL>", "kgstudio.payment.your-details": "您的详情", "kgstudio.payment.your-organization-name": "您的组织名称", "kgstudio.payment.your-organization-contact-email": "您的组织联系邮箱", "kgstudio.payment.payout-details": "提现详情", "kgstudio.payment.payout-due-date": "提现到期日", "kgstudio.payment.payout-due-date-placeholder": "YYYY/MM/DD", "kgstudio.payment.description-placeholder": "输入发票 ID 或关于此提现的任何其他描述", "kgstudio.payment.attachment-label": "附件", "kgstudio.payment.upload-attachment-desc": "在此上传发票或任何附件", "kgstudio.payment.payout-method": "提现方式", "kgstudio.payment.network": "网络", "kgstudio.payment.token": "代币", "kgstudio.payment.recipient-receiving-wallet": "收款人接收钱包", "kgstudio.payment.recipient-wallet-placeholder": "0xDaF09F6E6cEa6E08f4c7C32D4f71b54bdA02913", "kgstudio.payment.payout-amount": "提现金额", "kgstudio.payment.payout-amount-placeholder": "2000", "kgstudio.payment.payout-summary": "提现摘要", "kgstudio.payment.kryptogo-handling-fee": "KryptoGO 手续费 (5%)", "kgstudio.payment.total-amount": "总金额", "kgstudio.payment.validation.recipient-name-required": "收款人姓名为必填项", "kgstudio.payment.validation.email-valid": "需要有效的电子邮件", "kgstudio.payment.validation.organization-name-required": "组织名称为必填项", "kgstudio.payment.validation.due-date-required": "到期日为必填项", "kgstudio.payment.validation.description-required": "描述为必填项", "kgstudio.payment.validation.network-required": "网络为必填项", "kgstudio.payment.validation.token-required": "代币为必填项", "kgstudio.payment.validation.recipient-wallet-required": "收款人钱包地址为必填项", "kgstudio.payment.validation.payout-amount-required": "提现金额为必填项", "kgstudio.payment.payer-address": "付款人地址", "kgstudio.payment.payment-chain-id": "支付链 ID", "kgstudio.payment.payment-failed": "付款失败", "kgstudio.payment.payment-intent-id": "付款意向 ID", "kgstudio.payment.payment-item-list": "产品", "kgstudio.payment.payment-link": "付款链接", "kgstudio.payment.payment-list": "订单历史记录", "kgstudio.payment.payout-list": "提现历史记录", "kgstudio.payment.payment-status": "付款状态", "kgstudio.payment.payment-success": "付款成功", "kgstudio.payment.payment-text": "顾客", "kgstudio.payment.payment-tx-hash": "付款哈希", "kgstudio.payment.pay-token": "付款代币", "kgstudio.payment.pending": "待处理", "kgstudio.payment.pricing-mode": "定价模式", "kgstudio.payment.pricing-mode-fiat": "法币", "kgstudio.payment.pricing-mode-crypto": "加密货币", "kgstudio.payment.pricing-mode-dynamic": "动态", "kgstudio.payment.pricing-mode-fixed": "已修复", "kgstudio.payment.product-currency": "货币", "kgstudio.payment.product-description": "产品描述", "kgstudio.payment.product-description-placeholder": "请输入产品描述", "kgstudio.payment.product-image": "产品图片", "kgstudio.payment.product-name": "商品名", "kgstudio.payment.product-name-placeholder": "请输入产品名称", "kgstudio.payment.product-price": "产品价格", "kgstudio.payment.product-price-placeholder": "请输入产品价格", "kgstudio.payment.received-amount": "收到的金额", "kgstudio.payment.refund": "退款", "kgstudio.payment.refund.address.placeholder": "输入地址", "kgstudio.payment.refund.address.text": "退款地址", "kgstudio.payment.refund-amount": "退款金额", "kgstudio.payment.refund.amount.placeholder": "输入金额", "kgstudio.payment.refund.amount.text": "金额", "kgstudio.payment.refund.button": "退款", "kgstudio.payment.refund.cancel": "取消", "kgstudio.payment.refund.confirm": "确认退款", "kgstudio.payment.refund.error": "启动退款时出错", "kgstudio.payment.refund.success": "退款已成功发起", "kgstudio.payment.refund.title": "退款付款", "kgstudio.payment.remove-field": "移除", "kgstudio.payment.required-field": "必填字段", "kgstudio.payment.required-fields": "必填字段", "kgstudio.payment.required-fields-description": "这些字段为创建付款意图所必需", "kgstudio.payment.optional-fields-description": "可包含的其他字段", "kgstudio.payment.amount-fiat-placeholder": "100.00", "kgstudio.payment.amount-crypto-placeholder": "0.1", "kgstudio.payment.result-failed": "失败", "kgstudio.payment.result-success": "成功", "kgstudio.payment.search-intent": "按意图 ID 搜索", "kgstudio.payment.select-client": "选择用户", "kgstudio.payment.select-client-first": "请先选择用户", "kgstudio.payment.sending": "正在发送...", "kgstudio.payment.send-test": "发送测试", "kgstudio.payment.sent": "已发送", "kgstudio.payment.sign-payload": "签名有效载荷", "kgstudio.payment.status": "状态", "kgstudio.payment.status-code": "状态码", "kgstudio.payment.status-completed": "已完成", "kgstudio.payment.status-expired": "已过期", "kgstudio.payment.status-failed": "失败", "kgstudio.payment.status-pending": "待处理", "kgstudio.payment.status-refunded": "已退款", "kgstudio.payment.success": "成功", "kgstudio.payment.success-message": "", "kgstudio.payment.success-message-desc": "", "kgstudio.payment.success-message-placeholder": "", "kgstudio.payment.success-url": "成功网址", "kgstudio.payment.success-url-placeholder": "输入成功重定向 URL", "kgstudio.payment.symbol": "符号", "kgstudio.payment.test": "测试", "kgstudio.payment.test-callback": "测试 Callback", "kgstudio.payment.test-callback-error": "发送测试 Callback 失败", "kgstudio.payment.test-callback-sent": "测试 Callback 成功发送", "kgstudio.payment.timestamp": "时间戳", "kgstudio.payment.type": "类型", "kgstudio.payment.type-payment": "付款", "kgstudio.payment.type-test": "测试", "kgstudio.payment.update-item-error": "更新付款项目失败", "kgstudio.payment.update-item-success": "付款项目已成功更新", "kgstudio.payment.upload-image-desc": "上传图片", "kgstudio.payment.url-copied": "链接已复制", "kgstudio.payment.url-hint": "重定向时，我们将附加 “？pid=payment_intent_id” 到这个网址", "kgstudio.payment.view-payment-page": "分享付款页面", "kgstudio.payment.webhook-url": "Webhook 网址", "kgstudio.payment.connect-wallet": "连接钱包", "kgstudio.payment.wallet-connected": "钱包已连接", "kgstudio.payment.wallet-disconnected": "钱包已断开", "kgstudio.payment.wallet-connection-failed": "钱包连接失败", "kgstudio.payment.disconnect-wallet": "断开钱包", "kgstudio.payment.copy-address": "复制地址", "kgstudio.payment.address-copied": "地址已复制", "kgstudio.payment.select-wallet-description": "选择要连接到应用程序的钱包", "kgstudio.payment.metamask-description": "使用 MetaMask 浏览器扩展连接", "kgstudio.payment.browser-wallet-description": "使用浏览器钱包连接", "kgstudio.payment.wallet-description": "使用此钱包连接", "kgstudio.payment.connecting": "连接中...", "kgstudio.payment.not-available": "不可用", "kgstudio.payment.wallet-security-notice": "仅与您信任的网站连接", "kgstudio.permissions.notification-description": "您可以查看有权访问系统的用户。如果您想编辑您的用户，请联系系统提供商。", "kgstudio.permissions.title": "用户", "kgstudio.permissions.user-id": "用户 ID", "kgstudio.review.ai-summary": "AI 摘要", "kgstudio.review.ai-summary-fail": "没有与此客户相关的负面消息，不需执行 AI 摘要。", "kgstudio.review.aml-risk": "洗钱风险", "kgstudio.review.btn": "审查", "kgstudio.review.case-details": "案件详情", "kgstudio.review.check-id": "检查證件", "kgstudio.review.created": "已创建", "kgstudio.review.details": "详情", "kgstudio.review.filter-placeholder": "法定姓名、电话、电子邮件、国民身份证、LINE ID", "kgstudio.review.high-risk": "高风险", "kgstudio.review.idv": "ID 验证", "kgstudio.review.id-verification": "ID 验证", "kgstudio.review.idv-fail": "失败", "kgstudio.review.idv-pass": "通過", "kgstudio.review.internal-note": "内部笔记", "kgstudio.review.internal-notes-length-limit": "内部备注最多可包含 50 个字符", "kgstudio.review.internal-notes-required": "当状态为 “拒绝” 时，需要提供内部备注", "kgstudio.review.kyc-status": "身份验证状态", "kgstudio.review.latest-submit": "最新提交时间", "kgstudio.review.low-risk": "低风险", "kgstudio.review.mid-risk": "中等风险", "kgstudio.review.name": "姓名", "kgstudio.review.name-screening": "姓名筛选", "kgstudio.review.personal-information": "个人信息", "kgstudio.review.process-ai": "AI 運算中...", "kgstudio.review.processing": "处理中", "kgstudio.review.review-detail": "審查详情", "kgstudio.review.reviewer": "审阅者", "kgstudio.review.review-result": "审查结果", "kgstudio.review.review-time": "审阅时间", "kgstudio.review.risk": "风险", "kgstudio.review.sanctioned": "已制裁", "kgstudio.review.sanctioned-false": "未命中", "kgstudio.review.sanctioned-true": "命中", "kgstudio.review.start-ai": "开始 AI 摘要", "kgstudio.review.submission": "提交", "kgstudio.review.summary": "摘要", "kgstudio.review.updated": "更新于", "kgstudio.search.email": "搜索电子邮件", "kgstudio.select.at-least-one": "请至少选择一件項目", "kgstudio.send.add-attachment": "添加附件", "kgstudio.send.attachment-required": "需要附件才能获得批准", "kgstudio.send.by-email": "电子邮件", "kgstudio.send.by-phone-number": "手机号码", "kgstudio.send.do-not-leave-page": "不要离开此页面。", "kgstudio.send.gasless-alert-button": "", "kgstudio.send.gasless-alert-desc": "", "kgstudio.send.gasless-alert-title": "", "kgstudio.send.gasless-modal-deducted-desc-1": "", "kgstudio.send.gasless-modal-deducted-desc-2": "", "kgstudio.send.gasless-modal-deducted-title": "", "kgstudio.send.gasless-modal-desc": "", "kgstudio.send.gasless-modal-full-title": "", "kgstudio.send.gasless-modal-title": "", "kgstudio.send.gas-token-balance": "主代币余额：{balance}（预计费用：{gasFee}）", "kgstudio.send.loading-hint": "如果区块链非常繁忙，交易可能需要比预期更长的时间。要查看交易状态和详细信息，请单击 Tx Hash。", "kgstudio.send.max": "", "kgstudio.send.note-placeholder": "输入交易备注并上传必要的附件以供批准。", "kgstudio.send.note-required": "需要交易单才能获得批准", "kgstudio.send.over-limit": "超过极限", "kgstudio.send.remaining-balance": "今日可转出的余额： ", "kgstudio.send.remaining-balance-today": "您今天的剩余余额为 {formattedCurrentLimit}U（每日限额为 {formattedDailyLimit}U）。", "kgstudio.send.review-note": "评论笔记", "kgstudio.send.send-confirm-alert": "一旦确认此交易，它将立即存在于区块链上并立即生效，无法恢复！请确保区块链，金额和收款人均正确无误。", "kgstudio.send.send-confirm-submit-desc-alert": "确保您的交易详情正确无误，因为提交后无法修改。", "kgstudio.send.send-confirm-submit-title-alert": "此交易仅在获得批准后发送。", "kgstudio.send.send-to": "发送至", "kgstudio.send.submit-request": "提交请求", "kgstudio.send.title": "发送资金", "kgstudio.send.to-user": "至用户", "kgstudio.send.transaction-attachment": "附件", "kgstudio.send.transaction-error-desc": "请在管理员存入足够的资金后重试", "kgstudio.send.transaction-error-title": "错误：库存不足", "kgstudio.send.transaction-note": "交易须知", "kgstudio.send.transaction-submit-success-desc": "您的交易已提交，将在批准和发布后执行。", "kgstudio.send.transaction-submit-success-title": "交易已提交", "kgstudio.send.transaction-success-desc": "您的交易请求已成功提交，很快就能查看交易结果。", "kgstudio.send.transaction-success-title": "交易成功发送", "kgstudio.send.tx-failed": "交易失败", "kgstudio.send.tx-failed-description": "转账失败，请重试或联系系统管理员。", "kgstudio.send.tx-in-progress": "交易进行中。", "kgstudio.send.tx-success": "交易成功！", "kgstudio.send.view-profile": "查看个人资料", "kgstudio.setting.api-keys": "API 密钥", "kgstudio.setting.create-api-key": "创建 API 密钥", "kgstudio.setting.create-first-api-key": "创建您的第一个 API 密钥即可开始使用", "kgstudio.setting.create-first-oauth-client": "创建您的第一个 OAuth 客户端即可开始使用", "kgstudio.setting.create-oauth-client": "创建 OAuth 客户端", "kgstudio.setting.no-api-keys": "没有 API 密钥", "kgstudio.setting.no-oauth-clients": "没有 OAuth 客户端", "kgstudio.setting.oauth-clients": "OAuth 客户端", "kgstudio.setting.org-settings": "组织设置", "kgstudio.setting.user.account-settings": "账户设置", "kgstudio.setting.user.add-address": "添加地址", "kgstudio.setting.user.add-new-address": "添加新地址", "kgstudio.setting.user.address-name": "地址名称", "kgstudio.setting.user.address-name-placeholder": "例如我的主钱包", "kgstudio.setting.user.api-keys": "API 密钥", "kgstudio.setting.user.api-keys-description": "API 密钥允许以编程方式访问您的 KryptoGo 工作室账户。它们应保持安全，切勿公开共享。", "kgstudio.setting.user.cancel": "取消", "kgstudio.setting.user.check-documentation": "查看文档", "kgstudio.setting.user.client-domain": "域", "kgstudio.setting.user.client-id": "客户端 ID", "kgstudio.setting.user.client-id-desc": "如果您没有客户端 ID，请将其添加到 [账户设置-> OAuth 客户端] 页面。", "kgstudio.setting.user.client-id-required": "客户端 ID 为必填项", "kgstudio.setting.user.client-name": "客户姓名", "kgstudio.setting.user.client-secret": "客户机密", "kgstudio.setting.user.client-type": "类型", "kgstudio.setting.user.close": "关闭", "kgstudio.setting.user.confirm-delete-api-key": "您确定要删除此 API 密钥吗？此操作无法撤消。", "kgstudio.setting.user.confirm-delete-oauth-client": "你确定要删除这个 OAuth 客户端吗？此操作无法撤消。", "kgstudio.setting.user.confirm-deletion": "确认删除", "kgstudio.setting.user.copy-prompt": "复制 AI 提示", "kgstudio.setting.user.create-api-key": "创建 API 密钥", "kgstudio.setting.user.create-client-title": "创建 OAuth 客户端", "kgstudio.setting.user.created": "已创建", "kgstudio.setting.user.create-first-api-key": "创建您的第一个 API 密钥即可开始使用", "kgstudio.setting.user.create-first-oauth-client": "创建您的第一个 OAuth 客户端即可开始使用", "kgstudio.setting.user.create-key-title": "创建 API 密钥", "kgstudio.setting.user.create-oauth-client": "创建 OAuth 客户端", "kgstudio.setting.user.default": "默认", "kgstudio.setting.user.delete": "删除", "kgstudio.setting.user.description": "描述", "kgstudio.setting.user.domain-format-note": "域名必须以 http://或 https://开头（例如 https://app.kryptogo.com）", "kgstudio.setting.user.edit": "编辑", "kgstudio.setting.user.error.add-address": "格式错误，无法添加 EVM 付款地址", "kgstudio.setting.user.error.copy-clipboard": "无法复制到剪贴板", "kgstudio.setting.user.error.create-api-key": "创建 API 密钥失败", "kgstudio.setting.user.error.create-oauth-client": "无法创建 OAuth 客户端", "kgstudio.setting.user.error.delete-api-key": "删除 API 密钥失败", "kgstudio.setting.user.error.domain-format": "域名必须以 http://或 https://开头", "kgstudio.setting.user.error.domain-required": "域名为必填项", "kgstudio.setting.user.error.fetch-api-keys": "无法获取 API 密钥", "kgstudio.setting.user.error.fetch-oauth-clients": "无法获取 OAuth 客户端", "kgstudio.setting.user.error.set-default-address": "未能设置默认地址", "kgstudio.setting.user.error.update-oauth-client": "无法更新 OAuth 客户端", "kgstudio.setting.user.joyride.back": "返回", "kgstudio.setting.user.joyride.close": "关闭", "kgstudio.setting.user.joyride.finish": "完成", "kgstudio.setting.user.joyride.next": "下一步", "kgstudio.setting.user.joyride.skip": "跳过", "kgstudio.setting.user.joyride.step1": "OAuth 客户端 ID（客户端 ID）允许您将您的应用程序与 KryptoGO 支付 SDK 服务集成。您还可以创建自己的客户端 ID 以集成到不同的应用程序中。", "kgstudio.setting.user.joyride.step2": "一键复制人工智能提示并将其粘贴到推荐的人工智能模型服务（claude-3.7-sonnect，gpt-o3）中，即可生成可以立即使用的加密支付网页。", "kgstudio.setting.user.key-description": "描述（可选）", "kgstudio.setting.user.key-name": "名稱", "kgstudio.setting.user.key-prefix": "密钥前缀", "kgstudio.setting.user.last-used": "上次使用", "kgstudio.setting.user.loading": "加载中...", "kgstudio.setting.user.logo": "徽标", "kgstudio.setting.user.manage-payment-addresses": "管理付款地址", "kgstudio.setting.user.no-api-keys": "没有 API 密钥", "kgstudio.setting.user.no-oauth-clients": "没有 OAuth 客户端", "kgstudio.setting.user.oauth-clients": "OAuth 客户端", "kgstudio.setting.user.oauth-clients-description": "OAuth 客户端允许您将应用程序与 KryptoGo 服务集成。每个客户都有唯一的 ID 和密钥，应确保其安全。", "kgstudio.setting.user.org-wallet": "组织钱包", "kgstudio.setting.user.payment-address-description": "付款地址用于接收客户的付款。", "kgstudio.setting.user.save-client-message": "这是您唯一一次看到客户端密钥。请将其复制并安全存储。向 KryptoGO 服务发出 API 请求时，使用客户端密钥对您的应用程序进行身份验证。", "kgstudio.setting.user.save-client-title": "重要：保存您的客户凭证", "kgstudio.setting.user.save-key-message": "这是您唯一一次看到此 API 密钥。请将其复制并安全存储。", "kgstudio.setting.user.save-key-title": "重要：保存您的 API 密钥", "kgstudio.setting.user.set-as-default": "设为默认", "kgstudio.setting.user.success.address-added": "EVM 付款地址已成功添加！", "kgstudio.setting.user.success.api-key-created": "API 密钥已成功创建", "kgstudio.setting.user.success.api-key-deleted": "API 密钥已成功删除", "kgstudio.setting.user.success.copied": "{item} 已复制到剪贴板", "kgstudio.setting.user.success.oauth-client-created": "OAuth 客户端已成功创建", "kgstudio.setting.user.success.oauth-client-updated": "OAuth 客户端已成功更新", "kgstudio.setting.user.success.org-wallet-default": "组织钱包设置为默认值", "kgstudio.setting.user.update-oauth-client": "更新 OAuth 客户端", "kgstudio.setting.user.wallet-address": "钱包地址", "kgstudio.setting.user.your-addresses": "你的地址", "kgstudio.team.delete.description": "你确定要停用 {currentMemberName}({currentMemberEmail}) 吗？他们将立即注销，并且将无法再次登录。", "kgstudio.team.delete.success": "“{name}({email})” 已从您的团队中移除。", "kgstudio.team.edit.title": "编辑团队成员", "kgstudio.team.invite.error.existed": "用户已经存在", "kgstudio.team.invite.error.rate-limit": "您已达到短時間內的邀请上限。请稍后再试。", "kgstudio.team.invite.sent": "邀请已发送！", "kgstudio.team.invite.text": "邀请", "kgstudio.team.invite.title": "邀请团队成员", "kgstudio.team.member.member-id.title": "员工 ID（非必填）", "kgstudio.team.member.name.placeholder": "例如：王安娜", "kgstudio.team.members": "团队成员", "kgstudio.team.name.validation.required": "名字不能为空", "kgstudio.team.permission-settings": "权限设置", "kgstudio.team.rate-limit.hint": "1 分钟后重新邀请", "kgstudio.team.remove-member": "停用成员", "kgstudio.team.resend": "重发邀请", "kgstudio.team.role.approver": "批准者", "kgstudio.team.role.aseet-pro": "AssetPro 角色", "kgstudio.team.role.asset-pro": "AssetPro 角色", "kgstudio.team.role.compliance": "Compliance 角色", "kgstudio.team.role-decription.aseet-pro.admin": "有对 AssetPro 的完全访问权限，包括查看国库、发送资产、批准、下载所有交易历史记录和设置成员", "kgstudio.team.role-decription.aseet-pro.approver": "可以查看 AssetPro 中的所有交易并批准、下载交易", "kgstudio.team.role-decription.aseet-pro.finance-manager": "可以查看 AssetPro 中的所有交易，发布或拒绝等待释放的交易，并查看资金库。", "kgstudio.team.role-decription.aseet-pro.trader": "可以发送每日限额的资产并查看自己的交易", "kgstudio.team.role-decription.compliance.admin": "拥有对 Compliance 的完全访问权限，包括审查、全球设置验证流程", "kgstudio.team.role-decription.compliance.reviewer": "只能审查和编辑合规案例", "kgstudio.team.role.description": "所有者拥有对贵组织订阅的模块的所有功能的完全访问权限。", "kgstudio.team.role.finance-manager": "财务经理", "kgstudio.team.role.nft-boost": "NFT Boost 角色", "kgstudio.team.role.owner": "所有者", "kgstudio.team.role.reviewer": "审阅者", "kgstudio.team.role.title": "团队角色", "kgstudio.team.role.trader": "交易者", "kgstudio.team.role.user360": "用户 360 角色", "kgstudio.team.role.validation": "请至少选择一个模块角色", "kgstudio.team.role.wallet": "钱包角色", "kgstudio.team.text": "小组", "kgstudio.time.end-after-start": "结束时间必须晚于开始时间", "kgstudio.time.input-time": "请输入时间", "kgstudio.transaction.export": "出口", "kgstudio.transaction.operator": "操作员", "kgstudio.transaction.placeholder": "收款人的电子邮件、电话、地址、交易 Hash", "kgstudio.transactions.recipient-placeholder": "电子邮件、电话、地址", "kgstudio.transaction.status-awaiting-approval": "等待批准", "kgstudio.transaction.status-awaiting-release": "等待发布", "kgstudio.transaction.status-failed": "发送失败", "kgstudio.transaction.status-pending": "待批准 ", "kgstudio.transaction.status-reject": "已拒绝", "kgstudio.transaction.status-rejected": "已拒绝", "kgstudio.transaction.status-sending": "正在发送", "kgstudio.transaction.status-success": "成功发送", "kgstudio.transactions.title": "交易记录", "kgstudio.transaction.submit-time": "提交时间", "kgstudio.transactions.user-placeholder": "身份证、地址", "kgstudio.treasury.add-fund": "充值", "kgstudio.treasury.add-fund-modal.desc": "扫描二维码将您的资产充值到此钱包地址", "kgstudio.treasury.agree-and-continue": "我明白风险", "kgstudio.treasury.asset": "资产", "kgstudio.treasury.asset-name": "资产名称", "kgstudio.treasury.buy-crypto.desc": "买币說明", "kgstudio.treasury.buy-crypto.margin": "刷卡买币利润率 (%)", "kgstudio.treasury.buy-crypto-title": "刷卡买币", "kgstudio.treasury.chart": "图表", "kgstudio.treasury.click-to-refresh": "点击刷新", "kgstudio.treasury.click-to-reveal": "点击下面的按钮显示你的私钥", "kgstudio.treasury.contract-address": "合同地址", "kgstudio.treasury.deposit-tooltip": "余额低於 {alert_threshold}{symbol}！添加资金以确保交易成功。", "kgstudio.treasury.gas-swap.desc": "Gas Swap 描述", "kgstudio.treasury.gas-swap.margin": "Gas Swap 利润率 (%)", "kgstudio.treasury.gas-swap-title": "Gas 交换", "kgstudio.treasury.liquidity-settings": "流动性设置", "kgstudio.treasury.liquidity-settings-min-max": "最小值：{min}%，最大值：{max}%。", "kgstudio.treasury.liquidity-type": "流动性类型", "kgstudio.treasury.liquidity-update-confirm.btn": "是", "kgstudio.treasury.liquidity-update-confirm.message": "您的所有客户都将看到新的报价。", "kgstudio.treasury.liquidity-update-confirm.text": "你确定要更新利润率吗？", "kgstudio.treasury.liquidity-update-success": "成功更新！", "kgstudio.treasury.network": "网络", "kgstudio.treasury.price": "价格", "kgstudio.treasury.profit-current-rate": "当前利润率", "kgstudio.treasury.profit-description": "描述", "kgstudio.treasury.profit-margin": "利润率", "kgstudio.treasury.profit-margin-desc": "使用动态定价根据市场价格、手续费和利润率计算用户支出。", "kgstudio.treasury.profit-margin-rate": "利润率", "kgstudio.treasury.profit-margin-setting": "利润率设置", "kgstudio.treasury.profit-rate-edit": "编辑利润率", "kgstudio.treasury.profit-rate-edit-title": "设定利润率 (%)", "kgstudio.treasury.profit-rate-setting": "利润率设置", "kgstudio.treasury.profit-rate-update-success": "成功更新！", "kgstudio.treasury.profit-rate-update-text": "你确定要更新利润率吗？", "kgstudio.treasury.profit-service": "服务", "kgstudio.treasury.profit-service-bridge": "桥接（De<PERSON>i 上的跨链交换）", "kgstudio.treasury.profit-service-bridge-desc1": "支援 Tron、Ethereum、Arbitrum、Polygon、Base、Optimism 和 Binance Smart Chain 之间的跨链交换。例如：将 USDT(Tron) 交换为 ETH(Arbitrum)，您将获得原始代币 USDT(Tron) 作为收益。", "kgstudio.treasury.profit-service-bridge-desc2": "例如：客户支付了价值 100 美元的 USDT(Tron) 进行跨链交换以获得 ETH(Arbitrum)。如果企业将利润率设定为 2%，则客户将获得价值 100 * (1 - 2%) = 98 美元的 ETH(Arbitrum)。", "kgstudio.treasury.profit-service-bridge-hint1": "客户每交换 100 美元，您将赚取 $n。如果利润率为 2%，则 n = (100) - 100 * (1 - 2%) = 2 美元。", "kgstudio.treasury.profit-service-bridge-info1": "为去中心化代币交换设定利润率（%）。此比率将减少客户收到的代币数量。", "kgstudio.treasury.profit-service-bridge-info2": "*流动性由去中心化服务提供。", "kgstudio.treasury.profit-service-buy": "购买", "kgstudio.treasury.profit-service-buy-desc1": "如果您将利润率设置为1％，并且客户花费100美元购买加密货币\n=> 他们将获得 100/ (1+7% +1％) = 92.59 美元。您将获得美元作为收入。", "kgstudio.treasury.profit-service-buy-desc2": "（1％是您的利润率，7％是购买服务的市场成本）", "kgstudio.treasury.profit-service-buy-hint1": "设置购买代币的利润率（%）。该费率将添加到代币的市场价格中。", "kgstudio.treasury.profit-service-buy-info1": "客户每花费100美元，您将获得n美元。", "kgstudio.treasury.profit-service-buy-info2": "{profit_rate}{profit_usd}如果利润率 ={profit_rate}%，n = 100/ (1 +7%) -100/ (1 +7% +%) = 美元", "kgstudio.treasury.profit-service-send": "发送 (with TRX)", "kgstudio.treasury.profit-service-send-batch": "发送（批量）", "kgstudio.treasury.profit-service-send-batch-desc1": "（批量（收集）发送服务仅收取初始网络费用。现在没有利润率可供设置）", "kgstudio.treasury.profit-service-send-desc1": "当客户将Tron上的任何代币发送给朋友，并且他们有足够的TRX作为汽油代币时，您将获得汽油代币（TRX）的一部分作为收入。（“发送” 的利润率仅适用于 Tron。）", "kgstudio.treasury.profit-service-send-desc2": "例如，一位客户想向朋友汇款12.5泰达币（TRC20）作为午餐费用，而最初的汽油费为3 TRX。该企业将利润率定为10％。 \n=> 好友将获得 12.5 泰达币，而客户将发送 12.5 泰达币和 3.3 TRX 作为汽油费，因为汽油费将为 3 * (1 + 10％) = 3.3 TRX。", "kgstudio.treasury.profit-service-send-gasless": "发送（Gasless）", "kgstudio.treasury.profit-service-send-gasless-desc1": "当客户在没有足够的 TRX 作为汽油代币的情况下将 TRX 上的任何代币发送给朋友时，您将获得部分来源代币（例如USDT）作为收入。（“Gasless Send” 的利润率仅适用于 Tron）", "kgstudio.treasury.profit-service-send-gasless-desc2": "例如，一位客户想向朋友汇款12.5泰达币（TRC20）作为午餐费用，而最初的汽油费为 3 TRX。该企业将利润率定为10％。 \n=> 朋友将获得12.5泰达币，而客户将总共发送14.26泰达币。 \n=> 为了支付初始成本和整个交易，智能合约需要10 TRX 作为汽油费。 \n=> 礦工服務費 = 10 * (1 + 10％) = 11 TRX ~ 1.76 美元 \n=> 客户将发送 1.76 + 12.5 = 14.26 USDT。", "kgstudio.treasury.profit-service-send-gasless-hint1": "设置无气发送的利润率 (%)。该费率将影响从客户钱包发送的源代币的实际金额。", "kgstudio.treasury.profit-service-send-gasless-info1": "根据网络的繁忙程度，客户每发送一笔Tron交易，您可能会获得n美元的收入。", "kgstudio.treasury.profit-service-send-gasless-info2": "{profit_usd}如果利润率 ={profit_rate}%，n = 10 *{profit_rate}% = {profit_trx}TRX ~ 美元", "kgstudio.treasury.profit-service-send-hint1": "设置发送代币的利润率 (%)。此费率将增加客户支付的汽油费。", "kgstudio.treasury.profit-service-send-hint2": "*仅支持 TRC20-USDT 交易。", "kgstudio.treasury.profit-service-send-info1": "根据网络的繁忙程度，客户每发送一笔Tron交易，您可能会获得n美元的收入。", "kgstudio.treasury.profit-service-send-info2": "{profit_usd}如果利润率 ={profit_rate}%，n = 3*{profit_rate}% = {profit_trx}TRX ~ 美元", "kgstudio.treasury.profit-service-swap-cefi": "换幣 (Ce<PERSON><PERSON>(AssetPro))", "kgstudio.treasury.profit-service-swap-cefi-desc1": "仅适用于 Tron (TRC20) 代币。如果客户提交天然气交换请求（泰达币（Tron）-> TRX），您将获得目標代币（TRX）作为收入。", "kgstudio.treasury.profit-service-swap-cefi-desc2": "例如，一位客户支付了一些相当于100美元的泰达币来换成TRX。区块链能源租赁成本为5美元。如果企业将利润率设定为10％， \n=> 客户将获得相当于 (100-5) * (1-10％) = 85.5 美元的 TRX。", "kgstudio.treasury.profit-service-swap-cefi-hint1": "设置集中式代币互换的利润率（%）。该费率将减少客户收到的代币数量。", "kgstudio.treasury.profit-service-swap-cefi-hint2": "*流动性由您的AssetPro财政部提供。", "kgstudio.treasury.profit-service-swap-cefi-info1": "客户每交换100美元的交易量，您可能会获得n美元的收入，具体取决于网络的繁忙程度。", "kgstudio.treasury.profit-service-swap-cefi-info2": "如果利润率 ={profit_rate}%，n = (100-5)-[(100-5) * (1-{profit_rate}%)] = {profit_usd}美元", "kgstudio.treasury.profit-service-swap-defi": "换币（De<PERSON>i）", "kgstudio.treasury.profit-service-swap-defi-desc1": "流动性来自第三方去中心化金融服务，支持以太坊、Arbitrum、Polygon和币安智能链。例如，将SUSHI兑换成POL，您将获得源代币（SUSHI）的美元价值作为收入。", "kgstudio.treasury.profit-service-swap-defi-desc2": "例如，一位客户支付了一些相当于100美元的寿司来换成 POL。如果企业将利润率设定为10％， \n=> 客户将获得相当于 100* (1-10%) = 90 美元的 POL。", "kgstudio.treasury.profit-service-swap-defi-hint1": "设置去中心化代币互换的利润率（%）。提高此费率将减少客户收到的代币数量。", "kgstudio.treasury.profit-service-swap-defi-hint2": "*去中心化服务提供的流动性。", "kgstudio.treasury.profit-service-swap-defi-info1": "客户每交换100美元的交易量，您将获得n美元。", "kgstudio.treasury.profit-service-swap-defi-info2": "{profit_usd}如果利润率 ={profit_rate}%，n = (100)-100* (1-{profit_rate}%) = 美元", "kgstudio.treasury.quantity": "数量", "kgstudio.treasury.retrieve-balance-error": "", "kgstudio.treasury.reveal-seedphrase": "显示私钥", "kgstudio.treasury.seedphrase-warning-1": "您的私钥可以完全访问您的钱包和资金。", "kgstudio.treasury.seedphrase-warning-2": "切勿与任何人共享您的私钥或将其数字化存储。", "kgstudio.treasury.seedphrase-warning-3": "如果您的私钥或资金丢失或被盗，KryptoGO 无法找回您的私钥或资金。", "kgstudio.treasury.seedphrase-warning-4": "KryptoGO 对私钥泄露造成的任何损失概不负责。", "kgstudio.treasury.seedphrase-warning-title": "重要安全警告", "kgstudio.treasury.token": "代币", "kgstudio.treasury.token-price": "代币价格", "kgstudio.treasury.value": "价值", "kgstudio.user360.wallet-usage-registered": "已激活", "kgstudio.user360.wallet-usage-unregistered": "未激活", "kgstudio.user-dna.7-day-active": "7 天活跃", "kgstudio.user-dna.applied_at": "提交于", "kgstudio.user-dna.app-open-times": "应用程序开放时间", "kgstudio.user-dna.approved": "已批准", "kgstudio.user-dna.approved_at": "批准时间", "kgstudio.user-dna.dapp-favorites": "dApp 收藏夹", "kgstudio.user-dna.delay-hint": "由于区块链上的活动水平，钱包地址和余额信息偶尔可能会出现延迟。", "kgstudio.user-dna.dob": "出生日期", "kgstudio.user-dna.email": "电子邮件", "kgstudio.user-dna.first-apply": "首先申请", "kgstudio.user-dna.first-web3-activity": "第一个 Web3 活动", "kgstudio.user-dna.last-active": "上次活跃", "kgstudio.user-dna.last-apply": "上次申请", "kgstudio.user-dna.last-login": "上次登录", "kgstudio.user-dna.low": "低", "kgstudio.user-dna.name": "姓名", "kgstudio.user-dna.nation": "国家", "kgstudio.user-dna.national-id": "国家身份证", "kgstudio.user-dna.non-kyc-user": "未 KYC 用户", "kgstudio.user-dna.no-wallet": "该客户现在没有任何钱包。", "kgstudio.user-dna.of-10k-users": "在 1 万名用户中", "kgstudio.user-dna.phone": "电话", "kgstudio.user-dna.real-name": "真实姓名", "kgstudio.user-dna.registered": "已下载并登录", "kgstudio.user-dna.risk-score": "风险评分", "kgstudio.user-dna.sign-times": "签名时间", "kgstudio.user-dna.status": "KYC 状态", "kgstudio.user-dna.submission": "提交", "kgstudio.user-dna.title": "用户档案", "kgstudio.user-dna.transactions-volume": "交易量", "kgstudio.user-dna.tvl": "TVL", "kgstudio.user-dna.wallet-activity": "钱包活动", "kgstudio.user-dna.wallet-app-activities": "钱包应用程序活动", "kgstudio.user-dna.wallets": "钱包", "kgstudio.user-dna.wallet.tag": "标签", "kgstudio.validation.correct-format": "请输入正确的格式", "kgstudio.validation.number-greater-than-zero": "请输入一个大于 0 的值", "kgstudio.validation.phone-or-email-required": "资金只能转入已在您的钱包服务中注册的电子邮件地址或电话号码。", "kgstudio.validation.required": "请输入一个值", "kgstudio.validation.sorrect-format": "请输入正确的格式", "kgstudio.validation.valid-address": "请输入有效的地址", "kgstudio.validation.valid-email": "请输入有效的电子邮件", "kgstudio.validation.valid-phone": "请输入有效的电话号码", "kgstudio.wallet.active-features": "活跃功能", "kgstudio.wallet.app-images.app-icon": "应用程序图标", "kgstudio.wallet.app-images.splash": "飞溅", "kgstudio.wallet.app-images.title": "应用程序图片", "kgstudio.wallet.app-settings": "应用程序设置", "kgstudio.wallet.app-under-review": "应用程序正在审核中，无法修改。", "kgstudio.wallet.button.view-demo": "演示", "kgstudio.wallet.config.android": "安卓（Google Play）", "kgstudio.wallet.config.app-image": "应用程序图片", "kgstudio.wallet.config.app-startup": "应用程序启动屏幕", "kgstudio.wallet.config.app-store-info": "应用商店信息", "kgstudio.wallet.config.brand-logo-alt": "品牌标志", "kgstudio.wallet.config.check": "查看", "kgstudio.wallet.config.completion.step1": "请打开 KrypTogo 钱包，扫描下方二维码预览您的钱包应用程序", "kgstudio.wallet.config.completion.step2": "如果需要调整，请返回之前的步骤来修改您的设置", "kgstudio.wallet.config.completion.step3": "确认设置正确后，点击下方按钮提交", "kgstudio.wallet.config.completion.title": "应用程序设置已完成！", "kgstudio.wallet.config.configure-later": "稍后配置", "kgstudio.wallet.config.configure-publish-data": "设置发布数据", "kgstudio.wallet.config.confirm-before-submit": "提交前，请确保所有图片和文字正确无误", "kgstudio.wallet.config.confirm-submit": "确认并提交您的应用程序的正式版版本", "kgstudio.wallet.config.data-verification": "请验证输入的信息", "kgstudio.wallet.config.desc.all-chains-desc": "KryptoGO 支持的所有链，包括以太坊、Polygon、BNB Chain、Arbitrum、KCC、Ronin、比特币、Solana、Tron。当 KryptoGO 更新支持的链时，你的钱包也将自动更新。", "kgstudio.wallet.config.desc.all-evm-chains-desc": "KryptoGO 支持的所有 EVM 链，包括以太坊、Polygon、BNB 链、Arbitrum、KCC、Ron<PERSON>。当KryptoGO 更新支持的 EVM 链时，你的钱包也将自动更新。", "kgstudio.wallet.config.desc.currency-desc": "主页显示掉期功能按钮，允许用户交换不同的交易对（支持 ETH、Polygon、BNB 单链交换）。", "kgstudio.wallet.config.desc.custom-desc": "从 KryptoGO 支持的链条中选择要显示的链。请注意：当 KryptoGO 更新支持的链时，你的钱包不会自动更新；你需要自己修改设置。", "kgstudio.wallet.config.desc.custom-token-description": "您可以将自定义代币添加到钱包的代币列表中并设置錨定定价。如果您不需要自定义代币，则可以跳过此问题。", "kgstudio.wallet.config.desc.dapp-list": "应用程序上线后，您可以随时更新您的 dApp 列表。如果您目前不需要自定义 dApp 列表，只需跳过此列表即可。", "kgstudio.wallet.config.desc.defi-desc": "去中心化金融", "kgstudio.wallet.config.desc.displayed-asset-type-desc": "资产类型将显示在钱包的主页上。", "kgstudio.wallet.config.desc.displayed-chains-desc": "您的钱包中将不支持或显示未选择的链。", "kgstudio.wallet.config.desc.english": "EN-US（默认）", "kgstudio.wallet.config.desc.explore-dapp-browser-desc": "用户可以通过钱包中内置的 dApp 浏览器连接 dApp 操作。", "kgstudio.wallet.config.desc.japanese": "日本語", "kgstudio.wallet.config.desc.kyc-user-verification-desc": "与 Compliance Pro 功能相结合，用户可以提交 KYC 数据，您可以对其进行身份验证和尽职调查。", "kgstudio.wallet.config.desc.languages": "钱包应用程序语言", "kgstudio.wallet.config.desc.login-methods": "登录方法", "kgstudio.wallet.config.desc.nft-desc": "支持 ETH 和 Polygon 上的 NFT。", "kgstudio.wallet.config.desc.nft-rewards-desc": "结合 NFT Boost 功能，为用户提供可兑换的 NFT 赋权。", "kgstudio.wallet.config.desc.nft-sell-desc": "NFT 页面显示卖出按钮，允许用户一键在 OpenSea 上列出他们的 NFT（需要 dApp 浏览器功能）。", "kgstudio.wallet.config.desc.show-poap-desc": "收藏品显示用户拥有的 POAP NFT。", "kgstudio.wallet.config.desc.simplified-chinese": "中文（简体）", "kgstudio.wallet.config.desc.support-info": "资产类型将显示在钱包的主页上。未选择的资产类型不会显示在用户的钱包中。", "kgstudio.wallet.config.desc.swap-desc": "主页显示交换功能按钮，允许用户交换不同的区块链代币。（支持 ETH、Polygon、BNB、Arbitrum 单链交换。不包含菲亚特交易所。）", "kgstudio.wallet.config.desc.theme": "将您的品牌颜色应用于钱包应用程序", "kgstudio.wallet.config.desc.traditional-chinese": "中文（繁體）", "kgstudio.wallet.config.design-guideline": "应用程序图标设计指南", "kgstudio.wallet.config.extension": "网页扩展程序（Chrome 应用商店）", "kgstudio.wallet.config.file-type-supported": "支持的文件类型：JPG、PNG。最大大小：10 MB", "kgstudio.wallet.config.follow-guideline": "请关注", "kgstudio.wallet.config.get-started-description": "“入门” 屏幕上显示的图像。建议使用品牌徽标", "kgstudio.wallet.config.get-started-title": "入口图片", "kgstudio.wallet.config.ios": "iOS（App Store）", "kgstudio.wallet.config.label.add": "添加", "kgstudio.wallet.config.label.all-chains": "所有链", "kgstudio.wallet.config.label.all-evm-chains": "所有 EVM 链", "kgstudio.wallet.config.label.currency": "货币（必填）", "kgstudio.wallet.config.label.custom": "自定义", "kgstudio.wallet.config.label.custom-list": "自定义 DApp 列表", "kgstudio.wallet.config.label.custom-token": "自定义代币", "kgstudio.wallet.config.label.default-list": "KryptoGO 精选（UniSwap、OpenSea、SushiSwap、Rarible、Dune 等。）", "kgstudio.wallet.config.label.defi": "去中心化金融", "kgstudio.wallet.config.label.email": "电子邮件", "kgstudio.wallet.config.label.explore-dapp-browser": "探索 - DApp 浏览器", "kgstudio.wallet.config.label.kyc-user-verification": "KYC：用户验证", "kgstudio.wallet.config.label.nft": "NFT", "kgstudio.wallet.config.label.nft-rewards": "NFT 奖励", "kgstudio.wallet.config.label.nft-sell": "NFT 卖出（OpenSea）", "kgstudio.wallet.config.label.phone": "电话", "kgstudio.wallet.config.label.preview": "预览", "kgstudio.wallet.config.label.pro": "Pro", "kgstudio.wallet.config.label.show-poap": "显示 POAP", "kgstudio.wallet.config.label.swap": "加密货币交换", "kgstudio.wallet.config.processing-settings": "请稍候，我们正在处理您的设置", "kgstudio.wallet.config.promote-banner.desc": "应用程序上线后，您可以随时更新您的横幅。如果你现在不需要设置横幅，请跳过这个问题。", "kgstudio.wallet.config.promote-banner.title": "宣传横幅", "kgstudio.wallet.config.publish-settings-confirm-title": "发布设置数据已确认。你想立即提交制作吗？", "kgstudio.wallet.config.publish-settings-description": "KryptoGO 团队将立即根据您的应用程序设置创建您的钱包，并将其与发布数据一起提交给应用商店审核。如果一切顺利而无需修改，则可以在大约 14 个工作日内上市。（如果现在没有提交供生产，请稍后在钱包项目页面上单击 “提交”）", "kgstudio.wallet.config.recommended-size": "建议尺寸：1024 x 1024 px", "kgstudio.wallet.config.retry-or-contact": "请稍后重试或联系客服", "kgstudio.wallet.config.scanInstruction": "打开您的 KrypTogo 钱包上的扫描仪并扫描二维码进行本地预览", "kgstudio.wallet.config.scanToDemo.title1": "扫描以演示", "kgstudio.wallet.config.scanToDemo.title2": "在你的 KryptoGO 钱包里", "kgstudio.wallet.config.shelf-platform-title": "即将发布的平台", "kgstudio.wallet.config.splash-file-type-supported": "支持的文件类型：JPG、PNG。最大大小：10 MB", "kgstudio.wallet.config.splash-recommended-size": "推荐尺寸：1080 x 1920 px", "kgstudio.wallet.config.splash-screen-title": "启动画面", "kgstudio.wallet.config.steps.check": "查看", "kgstudio.wallet.config.steps.contact": "联系我们", "kgstudio.wallet.config.steps.explorer": "浏览器", "kgstudio.wallet.config.steps.explorer-hint": "用户可以通过资源管理器连接到各种 dApp。您可以自定义向用户推荐的 dApp 列表，还可以添加用于广告和指导的横幅。请放心，应用程序上线后，您可以随时更改这些设置。", "kgstudio.wallet.config.steps.feature": "特征", "kgstudio.wallet.config.steps.theme": "主题色", "kgstudio.wallet.config.store-display-info": "设置您的应用商店显示数据", "kgstudio.wallet.config.submission-description": "KryptoGO 团队将根据您的应用程序设置立即创建您的钱包，并将其提交给应用商店审核。如果一切顺利而无需修改，则可以在大约 14 个工作日内上市。", "kgstudio.wallet.config.submission-failed": "提交失败！", "kgstudio.wallet.config.submission-failed-description": "提交失败，请稍后再试", "kgstudio.wallet.config.submission-in-progress": "钱包应用程序正在提交中...", "kgstudio.wallet.config.submission-in-progress-description": "请稍候，正在提交您的 App...", "kgstudio.wallet.config.submission-successful": "提交成功！", "kgstudio.wallet.config.submit-failed": "应用程序设置提交失败", "kgstudio.wallet.config.submit-later": "稍后提交", "kgstudio.wallet.config.submit-success": "应用程序设置已提交。是否要继续进行发布设置？", "kgstudio.wallet.config.submitting-app-settings": "正在提交应用程序设置...", "kgstudio.wallet.config.tabs.dex": "去中心化交易所", "kgstudio.wallet.config.tabs.hot": "熱門", "kgstudio.wallet.config.tabs.nft": "NFT", "kgstudio.wallet.config.title.dapp-list": "dApp 列表", "kgstudio.wallet.config.title.displayed-asset-type": "显示的资产类型", "kgstudio.wallet.config.title.displayed-chains": "显示的区块链", "kgstudio.wallet.config.title.enable-features": "选择要启用的功能", "kgstudio.wallet.config.title.help-center-url": "帮助中心网址", "kgstudio.wallet.config.title.languages": "语言", "kgstudio.wallet.config.title.login-methods": "登录方法", "kgstudio.wallet.config.title.primary-color": "原色", "kgstudio.wallet.config.title.privacy-policy-url": "隐私政策网址", "kgstudio.wallet.config.title.secondary-color": "次要颜色", "kgstudio.wallet.config.title.support-email": "支持电子邮件", "kgstudio.wallet.config.title.support-info": "支持信息", "kgstudio.wallet.config.title.terms-condition-url": "使用条款网址", "kgstudio.wallet.config.title.theme": "主题色", "kgstudio.wallet.customized-dapp-list": "自定义 dApp 列表", "kgstudio.wallet.dex": "DEX", "kgstudio.wallet.explorer-banner": "瀏覽器首頁廣告 Banner", "kgstudio.wallet.extension.subtitle": "Chrome 网上应用店", "kgstudio.wallet.extension.title": "網頁插件", "kgstudio.wallet.feature.explore-dapp": "探索-dApp 浏览器", "kgstudio.wallet.feature.nft-rewards": "NFT 奖励", "kgstudio.wallet.feature.nft-sell": "NFT 卖出", "kgstudio.wallet.feature-settings": "功能设置", "kgstudio.wallet.feature.show-kyc": "显示 KYC", "kgstudio.wallet.feature.show-poap": "显示 POAP", "kgstudio.wallet.feature.swap": "兌换", "kgstudio.wallet.google-play.subtitle": "谷歌商城", "kgstudio.wallet.google-play.title": "安卓", "kgstudio.wallet.help-center": "帮助中心", "kgstudio.wallet.hot": "Hot", "kgstudio.wallet.ios.subtitle": "应用商店", "kgstudio.wallet.ios.title": "iOS", "kgstudio.wallet.language.vietnamese": "Tiếng <PERSON>", "kgstudio.wallet.mock.content": "在提交到应用程序平台之前，请确保使用组织名称创建您的应用商店开发者账户。根据应用商店的政策，个人开发者不能发布加密货币钱包应用程序。如需了解更多详情，请访问应用商店的发布指南。", "kgstudio.wallet.nft": "NFT", "kgstudio.wallet.privacy-policy": "隐私政策", "kgstudio.wallet.processing": "请稍候，我们正在处理您的设置。", "kgstudio.wallet.project-image": "项目图片", "kgstudio.wallet.project-name": "项目名称", "kgstudio.wallet.publish-settings": "发布设置", "kgstudio.wallet.setProjectTitle": "设置您的钱包", "kgstudio.wallet.status.draft": "草稿", "kgstudio.wallet.status.in-review": "平台评论中", "kgstudio.wallet.status.published": "已发布", "kgstudio.wallet.supported-chains": "支持的链", "kgstudio.wallet.supported-links": "支持的链接", "kgstudio.wallet.support-email": "支持电子邮件", "kgstudio.wallet.terms-condition": "条款和条件", "kgstudio.wallet.theme.primary-color": "原色", "kgstudio.wallet.theme.secondary-color": "次要颜色", "kgstudio.wallet.theme.title": "主题", "kgstudio.wallet.use-recommended-option": "使用推荐的选项", "page.page-size-description": "每页显示行数：{pageSize}", "permissions.notification-description": "您可以查看有权访问系统的用户。如果您想编辑您的用户，请联系系统提供商。", "permissions.title": "用户", "permissions.user-id": "用户 ID", "send.by-email": "电子邮件", "send.by-phone-number": "手机号码", "send.do-not-leave-page": "不要离开此页面。", "send.loading-hint": "如果区块链非常繁忙，交易可能需要比预期更长的时间。要查看交易状态和详细信息，请单击 Tx Hash。", "send.over-limit": "超过极限", "send.remaining-balance": "今日可转出的余额： ", "send.remaining-balance-today": "您今天的剩余余额为 {formattedCurrentLimit}U（每日限额为 {formattedDailyLimit}U）。", "send.send-confirm-alert": "一旦确认此交易，它将立即存在于区块链上并立即生效，无法恢复！请确保区块链，金额和收款人均正确无误。", "send.send-to": "发送至", "send.title": "发送资金", "send.to-user": "至用户", "send.tx-failed": "交易失败", "send.tx-failed-description": "转账失败，请重试或联系系统管理员。", "send.tx-in-progress": "交易进行中。", "send.tx-success": "交易成功！", "transactions.recipient-placeholder": "电子邮件、电话、地址", "transactions.title": "交易历史", "transactions.user-placeholder": "身份证、地址", "validation.correct-format": "请输入正确的格式", "validation.number-greater-than-zero": "请输入一个大于 0 的值", "validation.phone-or-email-required": "您只能对已使用「电子邮件」或「电话号码」在此钱包中注册的使用者转出资金。", "validation.required": "请输入一个值", "validation.sorrect-format": "请输入有效的格式。", "validation.valid-email": "请输入有效的电子邮件", "validation.valid-phone": "请输入有效的电话号码"}