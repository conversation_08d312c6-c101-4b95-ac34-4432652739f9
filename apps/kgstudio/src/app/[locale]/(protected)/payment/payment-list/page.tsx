'use client';

import { download, generateCsv, mkConfig } from 'export-to-csv';
import moment from 'moment';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import qs from 'qs';
import React, { useEffect, useMemo, useState } from 'react';
import JsonView from 'react18-json-view';
import 'react18-json-view/src/style.css';
import { useForm } from 'react-hook-form';
import { match } from 'ts-pattern';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { FilterItem, FormFilterGroup, FormMultiSelect } from '@/app/_common/components/form';
import { useDebounce, usePageHeader } from '@/app/_common/hooks';
import { ApiResponse, isApiError } from '@/app/_common/lib/api';
import internalAxiosInstance from '@/app/_common/lib/axios/instances/internal';
import { cn, removeEmptyArray, removeEmptyString } from '@/app/_common/lib/utils';
import { zodFilterSchema } from '@/app/_common/lib/zod';
import { apiPaymentHooks } from '@/app/_common/services';
import { PaymentChainIdSchema, PaymentStatusSchema } from '@/app/_common/services/payment/model';
import { useOrganizationStore } from '@/app/_common/store';
import { usePathname, useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, DataTable, Form, Tooltip, useDataTable, Modal } from '@kryptogo/2b';
import { getChainFullName } from '@kryptogo/utils';
import { formatCurrency, getExplorerUrl, truncateTxhashOrAddress } from '@kryptogo/utils';
import { ColumnDef, getCoreRowModel, TableOptions, useReactTable, VisibilityState } from '@tanstack/react-table';

import RefundModal from './_components/RefundModal';

const PaymentFilterSchema = z
  .object({
    status: z.array(PaymentStatusSchema).optional(),
    chain_id: PaymentChainIdSchema.optional(),
    client_id: z.string().optional(),
    group_key: z.string().optional(),
    collapsed_order_data: z.array(z.string()),
  })
  .strip();
type PaymentFilter = z.infer<typeof PaymentFilterSchema>;

// Custom API client using the internal axios instance
const api = {
  get: <T,>(url: string) => internalAxiosInstance.get<ApiResponse<T>>(url).then((response) => response.data),
  post: <T,>(url: string, data: any) =>
    internalAxiosInstance.post<ApiResponse<T>>(url, data).then((response) => response.data),
  delete: <T,>(url: string) => internalAxiosInstance.delete<ApiResponse<T>>(url).then((response) => response.data),
  put: <T,>(url: string, data: any) =>
    internalAxiosInstance.put<ApiResponse<T>>(url, data).then((response) => response.data),
};

interface OAuthClientResponse {
  client_id: string;
  client_name: string;
  client_domain: string;
  client_type: string;
  main_logo: string;
  square_logo?: string;
  created_at?: number;
  app_store_link?: string;
  google_play_link?: string;
}

// Add this new component for displaying JSON data
const JsonViewModal = ({ isOpen, onClose, data, t }: { isOpen: boolean; onClose: () => void; data: any; t: any }) => {
  if (!isOpen) return null;

  return (
    <Modal open={isOpen} onOpenChange={onClose}>
      <Modal.Content className="max-w-4xl">
        <Modal.Header>
          <Modal.Title>{t('kgstudio.payment.order-data')}</Modal.Title>
        </Modal.Header>
        <div className="max-h-[60vh] overflow-y-auto">
          <JsonView src={data} />
        </div>
      </Modal.Content>
    </Modal>
  );
};

const STORAGE_KEY = 'payment_columns_visibility';

// Add a custom hook for click outside detection
const useClickOutside = (handler: () => void) => {
  const ref = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        handler();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [handler]);

  return ref;
};

// Save column visibility to localStorage
const saveColumnVisibility = (visibility: VisibilityState) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(visibility));
  } catch (e) {
    console.error('Failed to save column visibility settings', e);
  }
};

// Load column visibility from localStorage
const loadColumnVisibility = (): VisibilityState | null => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    return saved ? JSON.parse(saved) : null;
  } catch (e) {
    console.error('Failed to load column visibility settings', e);
    return null;
  }
};

const PaymentList = () => {
  const pathname = usePathname();
  const router = useRouter();
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.payment.payment-list') });
  const params = useSearchParams();
  const parsedParams = zodFilterSchema(PaymentFilterSchema).parse(
    qs.parse(decodeURIComponent(params.toString()), { comma: true }),
  );

  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(() => {
    // Try to load saved settings, fallback to defaults
    return (
      loadColumnVisibility() || {
        payment_deadline: true,
        symbol: true,
        crypto_amount: true,
        aggregated_crypto_amount: true,
        refund_crypto_amount: false,
        payment_address: true,
        group_key: true,
        order_data: true,
        status: true,
        payment_tx_hash: false,
        client_id: false,
        actions: false,
      }
    );
  });

  const [tableOption, setTableOption] = useState<TableOptions<any>>({
    data: [],
    columns: [],
    manualSorting: true,
    manualPagination: true,
    getCoreRowModel: getCoreRowModel(),
    state: {
      columnVisibility,
    },
    onColumnVisibilityChange: setColumnVisibility,
  });
  const table = useReactTable(tableOption);
  const { page_number, page_size } = useDataTable(table);

  const [query, setQuery] = useState('');
  const debouncedQuery = useDebounce(query, 500);

  const form = useForm<PaymentFilter>({
    defaultValues: {
      status: undefined,
      chain_id: undefined,
      client_id: undefined,
      group_key: undefined,
      collapsed_order_data: [],
    },
    values: {
      ...parsedParams,
    },
    mode: 'onChange',
    resolver: zodResolver(PaymentFilterSchema),
  });

  const formStatus = form.watch('status');
  const formClientId = form.watch('client_id');
  const orgId = useOrganizationStore((state) => state.orgId);
  const isFilterSelected = useMemo(() => {
    return Object.values(removeEmptyString(removeEmptyArray(form.watch()))).some((v) => v !== undefined);
  }, [form]);

  const [oauthClients, setOAuthClients] = useState<Array<{ id: string; name: string }>>([]);
  const [selectedPaymentIntent, setSelectedPaymentIntent] = useState<{
    id: string;
    amount: string;
  } | null>(null);
  const [jsonModalData, setJsonModalData] = useState<{ isOpen: boolean; data: any }>({
    isOpen: false,
    data: null,
  });

  // Add state for dropdown visibility
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Create ref for click outside detection
  const dropdownRef = useClickOutside(() => {
    setIsDropdownOpen(false);
  });

  useEffect(() => {
    const fetchOAuthClients = async () => {
      if (!orgId) return;

      try {
        const response = await api.get<OAuthClientResponse[]>(`/studio/organization/${orgId}/oauth_clients`);
        setOAuthClients(response.data.map((client) => ({ id: client.client_id, name: client.client_name })));
      } catch (error) {
        if (isApiError(error)) {
          showToast(t('Error loading OAuth clients'), 'error');
        }
      }
    };

    fetchOAuthClients();
  }, [orgId]);

  const {
    data: paymentIntents,
    isLoading: paymentIntentsLoading,
    refetch: refetchPaymentIntents,
  } = apiPaymentHooks.useGetPaymentIntents(
    {
      params: {
        org_id: orgId as number,
      },
      queries: {
        page_size: page_size,
        page_number: page_number,
        client_id: formClientId ? formClientId : undefined,
        status: form.watch('status'),
        chain_id: form.watch('chain_id'),
        group_key: form.watch('group_key'),
        payout: false,
      },
      paramsSerializer: (params) => {
        return qs.stringify(params, { indices: false });
      },
    },
    {
      onError: (error) => {
        console.error('error', error);

        if (isApiError(error)) {
          showToast(t('Error loading payment intents'), 'error');
        }
      },
      refetchOnWindowFocus: true,
      refetchOnMount: true,
      refetchInterval: 30_000,
    },
  );

  const { data: downloadPaymentIntents } = apiPaymentHooks.useGetPaymentIntents(
    {
      params: {
        org_id: orgId as number,
      },
      queries: {
        page_size: 9999,
        page_number: 1,
        client_id: formClientId ? formClientId : undefined,
        status: form.watch('status'),
        chain_id: form.watch('chain_id'),
        group_key: form.watch('group_key'),
        payout: false,
      },
      paramsSerializer: (params) => {
        return qs.stringify(params, { indices: false });
      },
    },
    {
      onError: (error) => {
        console.error('error', error);

        if (isApiError(error)) {
          showToast(t('Error loading payment intents'), 'error');
        }
      },
    },
  );

  const filterItems = useMemo<FilterItem<PaymentFilter>[]>(() => {
    const clientOptions = oauthClients?.map((client) => ({
      label: client.name,
      value: client.id,
    }));

    return [
      {
        name: 'group_key',
        subject: t('kgstudio.payment.group-key'),
        type: 'input',
        placeholder: t('kgstudio.payment.group-key-search-placeholder'),
      },
      {
        name: 'status',
        subject: t('common.status.text'),
        type: 'checkbox',
        options: [
          { label: t('kgstudio.common.pending'), value: 'pending' },
          { label: t('kgstudio.common.success'), value: 'success' },
          { label: t('kgstudio.common.expired'), value: 'expired' },
          { label: t('kgstudio.common.insufficient_not_refunded'), value: 'insufficient_not_refunded' },
          { label: t('kgstudio.common.insufficient_refunded'), value: 'insufficient_refunded' },
        ],
      },
      {
        name: 'chain_id',
        subject: t('common.blockchain'),
        type: 'select',
        options: [
          { label: getChainFullName('arb')!, value: 'arb' },
          { label: getChainFullName('base')!, value: 'base' },
          { label: getChainFullName('optimism')!, value: 'optimism' },
        ],
      },
      {
        name: 'client_id',
        subject: t('kgstudio.setting.user.client-id'),
        type: 'select',
        options: clientOptions,
      },
    ];
  }, [t, oauthClients]);

  const columns: ColumnDef<any>[] = useMemo(
    () => [
      {
        accessorKey: 'payment_deadline',
        header: t('kgstudio.payment.deadline'),
        meta: {
          sortable: true,
          withoutPadding: true,
        },
        size: 100,
        cell: ({ row }) => {
          const { payment_deadline, status } = row.original;
          const borderColor = match(status)
            .with('pending', () => 'border-warning')
            .with('success', () => 'border-success')
            .with('expired', () => 'border-error')
            .with('insufficient_not_refunded', () => 'border-error')
            .with('insufficient_refunded', () => 'border-success')
            .exhaustive();
          return (
            <>
              <div className={cn('absolute left-0 top-0 h-full border-l-4', borderColor)}></div>
              <div className="w-full p-4">
                <p className="text-primary text-body-2">{moment(payment_deadline * 1000).format('YYYY/MM/DD')}</p>
                <p className="text-primary text-body-2">{moment(payment_deadline * 1000).format('HH:mm')}</p>
              </div>
            </>
          );
        },
      },
      {
        accessorKey: 'symbol',
        header: t('common.token'),
        size: 100,
        cell: ({ row }) => {
          const { symbol, payment_chain_id } = row.original;
          return (
            <div className="flex items-center gap-3">
              <div>
                <p className="text-primary text-body-2-bold">{symbol}</p>
                <p className="text-secondary text-small">{getChainFullName(payment_chain_id)}</p>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'crypto_amount',
        header: t('common.amount'),
        size: 100,
        meta: {
          sortable: true,
        },
        cell: ({ row }) => {
          const { crypto_amount, fiat_amount, fiat_currency } = row.original;

          return (
            <>
              <p className="text-primary text-body-2-bold">{formatCurrency({ amount: crypto_amount })}</p>
              <p className="text-secondary text-small">
                {fiat_amount} {fiat_currency}
              </p>
            </>
          );
        },
      },
      {
        accessorKey: 'aggregated_crypto_amount',
        header: t('kgstudio.payment.aggregated-amount'),
        size: 100,
        meta: {
          sortable: true,
        },
        cell: ({ row }) => {
          const { aggregated_crypto_amount, symbol } = row.original;

          return (
            <p className="text-primary text-body-2-bold">
              {aggregated_crypto_amount ? formatCurrency({ amount: aggregated_crypto_amount }) : '-'}{' '}
              {aggregated_crypto_amount ? symbol : ''}
            </p>
          );
        },
      },
      {
        accessorKey: 'refund_crypto_amount',
        header: t('kgstudio.payment.refund-amount'),
        size: 100,
        meta: {
          sortable: true,
        },
        cell: ({ row }) => {
          const { refund_crypto_amount, symbol } = row.original;

          return (
            <p className="text-primary text-body-2-bold">
              {refund_crypto_amount ? formatCurrency({ amount: refund_crypto_amount }) : '-'}{' '}
              {refund_crypto_amount ? symbol : ''}
            </p>
          );
        },
      },
      {
        accessorKey: 'payment_address',
        header: t('kgstudio.common.address'),
        size: 150,
        cell: ({ row }) => {
          const { payment_address, payment_chain_id } = row.original;

          return (
            <Link
              className="text-brand-primary text-small"
              href={getExplorerUrl('address', payment_chain_id, payment_address) ?? ''}
              target="_blank"
              onClick={(e) => e.stopPropagation()}
            >
              {truncateTxhashOrAddress(payment_address)}
            </Link>
          );
        },
      },
      {
        accessorKey: 'group_key',
        header: t('kgstudio.payment.group-key'),
        size: 120,
        cell: ({ row }) => {
          const { group_key } = row.original;
          return <div className="text-primary text-body-2">{group_key || '-'}</div>;
        },
      },

      {
        accessorKey: 'order_data',
        header: t('kgstudio.payment.order-data'),
        size: 200,
        cell: ({ row }) => {
          const { order_data } = row.original;
          const isCollapsed = form.watch('collapsed_order_data').length === 0;

          if (!order_data) return '-';

          return (
            <div className="flex items-center">
              <JsonView src={order_data} collapsed={isCollapsed} />

              <Button
                variant="ghost"
                size="sm"
                className="ml-2"
                onClick={(e) => {
                  e.stopPropagation();
                  setJsonModalData({ isOpen: true, data: order_data });
                }}
              >
                <span className="text-xs">🔍</span>
              </Button>
            </div>
          );
        },
      },
      {
        accessorKey: 'status',
        header: t('common.status.text'),
        size: 140,
        meta: {
          sortable: true,
        },
        cell: ({ row }) => {
          const { status } = row.original;
          const statusText = match(status)
            .with('pending', () => t('kgstudio.common.pending'))
            .with('success', () => t('kgstudio.common.success'))
            .with('expired', () => t('kgstudio.common.expired'))
            .with('insufficient_not_refunded', () => t('kgstudio.common.insufficient_not_refunded'))
            .with('insufficient_refunded', () => t('kgstudio.common.insufficient_refunded'))
            .exhaustive();

          const statusClass = match(status)
            .with('pending', () => 'bg-warning-light text-warning-dark')
            .with('success', () => 'bg-success-light text-success-dark')
            .with('expired', () => 'bg-error-light text-error-dark')
            .with('insufficient_not_refunded', () => 'bg-error-light text-error-dark')
            .with('insufficient_refunded', () => 'bg-success-light text-success-dark')
            .exhaustive();

          return <div className={cn('inline-flex rounded-full px-2 py-1', statusClass)}>{statusText}</div>;
        },
      },
      {
        accessorKey: 'payment_tx_hash',
        header: t('common.tx-hash'),
        size: 150,
        cell: ({ row }) => {
          const { payment_tx_hash, payment_chain_id } = row.original;

          return !payment_tx_hash ? (
            ''
          ) : (
            <Link
              className="text-primary text-body-2 underline"
              href={getExplorerUrl('tx', payment_chain_id, payment_tx_hash) ?? ''}
              target="_blank"
              rel="noopener noreferrer"
              onClick={(e) => e.stopPropagation()}
            >
              {truncateTxhashOrAddress(payment_tx_hash)}
            </Link>
          );
        },
      },
      {
        accessorKey: 'client_id',
        header: t('kgstudio.setting.user.client-id'),
        size: 120,
        cell: ({ row }) => {
          const { client_id } = row.original;
          const clientName = oauthClients.find((client) => client.id === client_id)?.name || client_id;
          return (
            <Tooltip>
              <Tooltip.Trigger>
                <div className="text-primary text-body-2">{clientName}</div>
              </Tooltip.Trigger>
              <Tooltip.Content>
                <p className="text-primary text-body-2">{client_id}</p>
              </Tooltip.Content>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: 'actions',
        header: t('common.actions'),
        size: 100,
        cell: ({ row }) => {
          const { status, payment_intent_id, aggregated_crypto_amount } = row.original;
          if (status === 'insufficient_not_refunded') {
            return (
              <Button
                variant="secondary"
                onClick={() =>
                  setSelectedPaymentIntent({
                    id: payment_intent_id,
                    amount: aggregated_crypto_amount || '0',
                  })
                }
              >
                {t('kgstudio.payment.refund.button')}
              </Button>
            );
          }
          return null;
        },
      },
    ],
    [t, oauthClients],
  );

  useEffect(() => {
    setTableOption((prev) => ({
      ...prev,
      data: paymentIntents?.data || [],
      columns,
      state: {
        ...prev.state,
        columnVisibility,
      },
    }));
  }, [columns, paymentIntents, columnVisibility]);

  // Update localStorage when column visibility changes
  useEffect(() => {
    saveColumnVisibility(columnVisibility);
  }, [columnVisibility]);

  const handleDownload = async () => {
    const csvData = downloadPaymentIntents?.data?.map((original) => ({
      payment_deadline: moment(original.payment_deadline * 1000).format('YYYY/MM/DD HH:mm'),
      symbol: original.symbol,
      crypto_amount: formatCurrency({ amount: original.crypto_amount }),
      aggregated_crypto_amount: original.aggregated_crypto_amount
        ? formatCurrency({ amount: original.aggregated_crypto_amount })
        : '-',
      refund_crypto_amount: original.refund_crypto_amount
        ? formatCurrency({ amount: original.refund_crypto_amount })
        : '-',
      payment_address: original.payment_address,
      group_key: original.group_key,
      order_data: original.order_data ? JSON.stringify(original.order_data) : undefined,
      status: match(original.status)
        .with('pending', () => 'Pending')
        .with('success', () => 'Success')
        .with('expired', () => 'Expired')
        .with('insufficient_not_refunded', () => 'Insufficient Not Refunded')
        .with('insufficient_refunded', () => 'Insufficient Refunded')
        .exhaustive(),
      payment_tx_hash: original.payment_tx_hash,
      client_name: oauthClients.find((client) => client.id === original.client_id)?.name || original.client_id,
    }));

    const config = mkConfig({
      filename: `payment history ${moment().format('YY/MM/DD')}.csv`,
      columnHeaders: [
        { key: 'payment_deadline', displayLabel: t('kgstudio.payment.deadline') },
        { key: 'symbol', displayLabel: t('common.token') },
        { key: 'crypto_amount', displayLabel: t('common.amount') },
        { key: 'aggregated_crypto_amount', displayLabel: t('kgstudio.payment.aggregated-amount') },
        { key: 'refund_crypto_amount', displayLabel: t('kgstudio.payment.refund-amount') },
        { key: 'payment_address', displayLabel: t('kgstudio.common.address') },
        { key: 'group_key', displayLabel: t('kgstudio.payment.group-key') },
        { key: 'order_data', displayLabel: t('kgstudio.payment.order-data') },
        { key: 'status', displayLabel: t('common.status.text') },
        { key: 'payment_tx_hash', displayLabel: t('common.tx-hash') },
        { key: 'client_name', displayLabel: t('kgstudio.setting.user.client-id') },
      ],
    });
    const csv = generateCsv(config)(csvData || []);
    download(config)(csv);
  };

  return (
    <>
      <Form {...form}>
        <div className="my-6 flex flex-wrap items-center gap-3">
          <FormFilterGroup
            control={form.control}
            items={filterItems}
            data-cy="payment-filter-group"
            {...(isFilterSelected && {
              onClearFilter: () => {
                params.toString() ? router.push(pathname) : form.reset();
              },
            })}
          />
          <FormMultiSelect
            control={form.control}
            name="collapsed_order_data"
            items={[{ id: 'true', label: t('kgstudio.payment.order-data-expanded') }]}
            variant="vertical"
            withoutMessage
          />

          <div className="ml-auto">
            <div className="relative">
              <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="flex min-w-[180px] items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm text-gray-700"
              >
                <span>{t('kgstudio.payment.column-setting')}</span>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className={`transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
                >
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </button>
              {isDropdownOpen && (
                <div
                  ref={dropdownRef}
                  className="absolute right-0 z-10 mt-2 w-[200px] overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md"
                >
                  {[
                    { id: 'payment_deadline', label: t('kgstudio.payment.deadline') },
                    { id: 'symbol', label: t('common.token') },
                    { id: 'crypto_amount', label: t('common.amount') },
                    { id: 'aggregated_crypto_amount', label: t('kgstudio.payment.aggregated-amount') },
                    { id: 'refund_crypto_amount', label: t('kgstudio.payment.refund-amount') },
                    { id: 'payment_address', label: t('kgstudio.common.address') },
                    { id: 'group_key', label: t('kgstudio.payment.group-key') },
                    { id: 'order_data', label: t('kgstudio.payment.order-data') },
                    { id: 'status', label: t('common.status.text') },
                    { id: 'payment_tx_hash', label: t('common.tx-hash') },
                    { id: 'client_id', label: t('kgstudio.setting.user.client-id') },
                    { id: 'actions', label: t('common.actions') },
                  ].map(({ id, label }) => (
                    <div key={id} className="hover:bg-gray-50">
                      <label className="flex cursor-pointer items-center px-4 py-2">
                        <input
                          type="checkbox"
                          className="mr-3 h-4 w-4 accent-blue-500"
                          checked={columnVisibility[id] !== false}
                          onChange={(e) => {
                            const newVisibility = {
                              ...columnVisibility,
                              [id]: e.target.checked,
                            };
                            setColumnVisibility(newVisibility);
                            saveColumnVisibility(newVisibility);
                          }}
                        />
                        <span className="text-sm text-gray-700">{label}</span>
                      </label>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <Button variant="primary" size="md" onClick={handleDownload}>
            {t('kgstudio.payment.export-csv')}
          </Button>
        </div>
      </Form>

      <Card className="overflow-x-auto !p-0">
        <div className="w-full overflow-x-auto">
          <DataTable
            table={table}
            isLoading={paymentIntentsLoading}
            dataLength={paymentIntents?.paging?.total_count || 0}
          />
        </div>
      </Card>

      {jsonModalData.isOpen && (
        <JsonViewModal
          isOpen={jsonModalData.isOpen}
          onClose={() => setJsonModalData({ isOpen: false, data: null })}
          data={jsonModalData.data}
          t={t}
        />
      )}

      {selectedPaymentIntent && (
        <RefundModal
          isOpen={!!selectedPaymentIntent}
          onClose={() => setSelectedPaymentIntent(null)}
          paymentIntentId={selectedPaymentIntent.id}
          orgId={orgId as number}
          defaultAmount={selectedPaymentIntent.amount}
          onSuccess={() => {
            // Refetch the payment intents list
            refetchPaymentIntents();
          }}
        />
      )}
    </>
  );
};

export default PaymentList;
